#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Yemen Maps - Main Server Application
الخادم الرئيسي لنظام خرائط اليمن
"""

import os
import sys
import json
from flask import Flask, render_template, request, jsonify, send_from_directory
from flask_cors import CORS
import psycopg2
import psycopg2.extras
from datetime import datetime
import logging
from pathlib import Path
from advanced_apis import AdvancedAPIs
from advanced_routes import register_advanced_routes

# إعداد المسارات
BASE_DIR = Path(__file__).parent.parent
IMAGES_DIR = BASE_DIR / "images"
STATIC_DIR = BASE_DIR / "public"
TEMPLATES_DIR = BASE_DIR / "templates"

# إعداد التطبيق
app = Flask(__name__,
           static_folder=str(STATIC_DIR),
           template_folder=str(TEMPLATES_DIR))
CORS(app)

# إعداد قاعدة البيانات - استخدام قاعدة البيانات الموجودة
DB_CONFIG = {
    'host': 'localhost',
    'database': 'yemen_gps',
    'user': 'yemen',
    'password': 'admin',
    'port': 5432
}

# إعداد السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseManager:
    def __init__(self):
        self.conn = None

    def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            self.conn = psycopg2.connect(**DB_CONFIG)
            logger.info("Connected to database successfully")
            return True
        except Exception as e:
            logger.error(f"Database connection error: {e}")
            return False

    def disconnect(self):
        """قطع الاتصال"""
        if self.conn:
            self.conn.close()

    def execute_query(self, query, params=None):
        """تنفيذ استعلام"""
        try:
            if not self.conn or self.conn.closed:
                self.connect()

            cursor = self.conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            cursor.execute(query, params)
            return cursor.fetchall()
        except Exception as e:
            logger.error(f"Query execution error: {e}")
            return []

# إنشاء مدير قاعدة البيانات
db = DatabaseManager()

# إنشاء مدير APIs المتقدمة
advanced_apis = AdvancedAPIs(DB_CONFIG)

def initialize():
    """تهيئة التطبيق"""
    if not db.connect():
        logger.error("Failed to connect to database")
    logger.info("Application initialized successfully")

@app.route('/')
def index():
    """الصفحة الرئيسية - واجهة Google Maps"""
    return render_template('google-style.html')

@app.route('/basic')
def basic_index():
    """الصفحة الأساسية"""
    return render_template('index.html')

@app.route('/advanced')
def advanced_index():
    """الواجهة المتقدمة القديمة"""
    return render_template('advanced-index.html')

@app.route('/admin')
def admin():
    """لوحة الإدارة المتقدمة"""
    return render_template('admin-dashboard.html')

@app.route('/admin/basic')
def admin_basic():
    """لوحة الإدارة الأساسية"""
    return render_template('admin.html')

@app.route('/google-style')
def google_style():
    """واجهة بنمط Google Maps"""
    return render_template('google-style.html')

@app.route('/api/places')
def get_places():
    """جلب الأماكن"""
    try:
        if not db.conn or db.conn.closed:
            db.connect()

        # معاملات البحث
        lat = request.args.get('lat', type=float)
        lng = request.args.get('lng', type=float)
        radius = request.args.get('radius', 10, type=int)  # كيلومتر
        category = request.args.get('category', '')
        search = request.args.get('search', '')
        limit = request.args.get('limit', 100, type=int)

        # بناء الاستعلام الأساسي
        query = """
            SELECT
                p.id,
                COALESCE(p.place_id, 'place_' || p.id::text) as place_id,
                COALESCE(p.name, p.name_ar, 'مكان غير محدد') as name,
                p.name_ar,
                p.latitude,
                p.longitude,
                COALESCE(p.category, 'general') as category,
                p.subcategory,
                p.rating,
                p.phone,
                p.address,
                l.name_ar as governorate_name
            FROM places p
            LEFT JOIN locations l ON p.governorate_id = l.id
            WHERE COALESCE(p.is_active, true) = true
            AND p.latitude IS NOT NULL
            AND p.longitude IS NOT NULL
        """

        params = []

        # فلترة حسب الموقع
        if lat and lng:
            query += """
                AND ST_DWithin(
                    ST_SetSRID(ST_MakePoint(p.longitude, p.latitude), 4326),
                    ST_SetSRID(ST_MakePoint(%s, %s), 4326),
                    %s
                )
            """
            params.extend([lng, lat, radius * 1000])  # تحويل إلى متر

        # فلترة حسب الفئة
        if category:
            query += " AND COALESCE(p.category, 'general') = %s"
            params.append(category)

        # البحث النصي
        if search:
            query += " AND (p.name ILIKE %s OR p.name_ar ILIKE %s OR p.address ILIKE %s)"
            search_term = f"%{search}%"
            params.extend([search_term, search_term, search_term])

        query += " ORDER BY p.rating DESC NULLS LAST, p.id LIMIT %s"
        params.append(limit)

        places = db.execute_query(query, params)

        # تحويل النتائج
        result = []
        for place in places:
            place_data = dict(place)

            # البحث عن صورة رئيسية
            photo_query = """
                SELECT photo_path FROM place_photos
                WHERE place_id = %s AND is_primary = true AND is_active = true
                LIMIT 1
            """
            photos = db.execute_query(photo_query, [place_data['place_id']])

            if photos:
                place_data['primary_photo'] = f"/images/{os.path.basename(photos[0]['photo_path'])}"
            else:
                place_data['primary_photo'] = None

            result.append(place_data)

        return jsonify({
            'success': True,
            'places': result,
            'count': len(result)
        })

    except Exception as e:
        logger.error(f"Error getting places: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/place/<place_id>')
def get_place_details(place_id):
    """جلب تفاصيل مكان محدد"""
    try:
        if not db.conn or db.conn.closed:
            db.connect()

        # جلب بيانات المكان
        place_query = """
            SELECT
                p.*,
                l.name_ar as governorate_name
            FROM places p
            LEFT JOIN locations l ON p.governorate_id = l.id
            WHERE (p.place_id = %s OR p.id::text = %s)
            AND COALESCE(p.is_active, true) = true
        """

        places = db.execute_query(place_query, [place_id, place_id])
        if not places:
            return jsonify({'success': False, 'error': 'Place not found'}), 404

        place = dict(places[0])

        # جلب صور المكان
        photos_query = """
            SELECT photo_path, photo_type, is_primary, display_order
            FROM place_photos
            WHERE place_id = %s AND COALESCE(is_active, true) = true
            ORDER BY is_primary DESC, display_order ASC
        """

        photos = db.execute_query(photos_query, [place_id])
        place['photos'] = []

        for photo in photos:
            photo_data = dict(photo)
            photo_data['photo_url'] = f"/images/{os.path.basename(photo_data['photo_path'])}"
            place['photos'].append(photo_data)

        return jsonify({
            'success': True,
            'place': place
        })

    except Exception as e:
        logger.error(f"Error getting place details: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/categories')
def get_categories():
    """جلب فئات الأماكن"""
    try:
        if not db.conn or db.conn.closed:
            db.connect()

        query = """
            SELECT
                pc.name, pc.name_ar, pc.icon, pc.color,
                COUNT(p.id) as places_count
            FROM place_categories pc
            LEFT JOIN places p ON COALESCE(p.category, 'general') = pc.name
                AND COALESCE(p.is_active, true) = true
            WHERE COALESCE(pc.is_active, true) = true
            GROUP BY pc.id, pc.name, pc.name_ar, pc.icon, pc.color, pc.display_order
            ORDER BY pc.display_order ASC, places_count DESC
        """

        categories = db.execute_query(query)

        return jsonify({
            'success': True,
            'categories': [dict(cat) for cat in categories]
        })

    except Exception as e:
        logger.error(f"Error getting categories: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/governorates')
def get_governorates():
    """جلب المحافظات مع إحصائيات"""
    try:
        if not db.conn or db.conn.closed:
            db.connect()

        query = """
            SELECT
                l.id, l.name, l.name_ar, l.latitude, l.longitude,
                COALESCE(l.data_status, 'pending') as data_status,
                COALESCE(l.places_count, 0) as places_count,
                COALESCE(l.photos_count, 0) as photos_count,
                COUNT(p.id) as actual_places_count
            FROM locations l
            LEFT JOIN places p ON p.governorate_id = l.id
                AND COALESCE(p.is_active, true) = true
            WHERE l.type = 'governorate'
            GROUP BY l.id, l.name, l.name_ar, l.latitude, l.longitude,
                     l.data_status, l.places_count, l.photos_count
            ORDER BY l.name_ar
        """

        governorates = db.execute_query(query)

        return jsonify({
            'success': True,
            'governorates': [dict(gov) for gov in governorates]
        })

    except Exception as e:
        logger.error(f"Error getting governorates: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/stats')
def get_stats():
    """جلب إحصائيات متقدمة"""
    try:
        advanced_apis.connect_database()
        stats = advanced_apis.get_advanced_stats()

        return jsonify({
            'success': True,
            'stats': stats
        })

    except Exception as e:
        logger.error(f"Error getting advanced stats: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/search')
def search_places():
    """البحث المتقدم في الأماكن"""
    try:
        if not db.conn or db.conn.closed:
            db.connect()

        query_text = request.args.get('q', '').strip()
        limit = request.args.get('limit', 10, type=int)

        if not query_text:
            return jsonify({'success': True, 'places': []})

        # البحث في الأماكن
        search_query = """
            SELECT
                p.id,
                COALESCE(p.place_id, 'place_' || p.id::text) as place_id,
                COALESCE(p.name, p.name_ar, 'مكان غير محدد') as name,
                p.name_ar,
                p.name_en,
                p.latitude,
                p.longitude,
                p.category_id,
                p.rating,
                l.name_ar as governorate_name
            FROM places p
            LEFT JOIN locations l ON p.governorate_id = l.id
            WHERE COALESCE(p.is_active, true) = true
            AND p.latitude IS NOT NULL
            AND p.longitude IS NOT NULL
            AND (
                p.name_ar ILIKE %s OR
                p.name_en ILIKE %s OR
                p.address ILIKE %s OR
                l.name_ar ILIKE %s
            )
            ORDER BY
                CASE
                    WHEN p.name_ar ILIKE %s THEN 1
                    WHEN p.name_en ILIKE %s THEN 2
                    ELSE 3
                END,
                p.rating DESC NULLS LAST
            LIMIT %s
        """

        search_term = f"%{query_text}%"
        exact_term = f"{query_text}%"

        places = db.execute_query(search_query, [
            search_term, search_term, search_term, search_term,
            exact_term, exact_term, limit
        ])

        return jsonify({
            'success': True,
            'places': [dict(place) for place in places],
            'count': len(places)
        })

    except Exception as e:
        logger.error(f"Error searching places: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/places/nearby')
def get_nearby_places_main():
    """جلب الأماكن القريبة"""
    try:
        if not db.conn or db.conn.closed:
            db.connect()

        lat = request.args.get('lat', type=float)
        lng = request.args.get('lng', type=float)
        radius = request.args.get('radius', 5000, type=int)  # متر
        limit = request.args.get('limit', 50, type=int)

        if not lat or not lng:
            return jsonify({'success': False, 'error': 'Latitude and longitude required'}), 400

        # البحث عن الأماكن القريبة
        nearby_query = """
            SELECT
                p.id,
                COALESCE(p.place_id, 'place_' || p.id::text) as place_id,
                COALESCE(p.name, p.name_ar, 'مكان غير محدد') as name,
                p.name_ar,
                p.name_en,
                p.latitude,
                p.longitude,
                p.category_id,
                p.rating,
                l.name_ar as governorate_name,
                ST_Distance(
                    ST_SetSRID(ST_MakePoint(p.longitude, p.latitude), 4326),
                    ST_SetSRID(ST_MakePoint(%s, %s), 4326)
                ) * 111000 as distance_meters
            FROM places p
            LEFT JOIN locations l ON p.governorate_id = l.id
            WHERE COALESCE(p.is_active, true) = true
            AND p.latitude IS NOT NULL
            AND p.longitude IS NOT NULL
            AND ST_DWithin(
                ST_SetSRID(ST_MakePoint(p.longitude, p.latitude), 4326),
                ST_SetSRID(ST_MakePoint(%s, %s), 4326),
                %s
            )
            ORDER BY distance_meters ASC
            LIMIT %s
        """

        places = db.execute_query(nearby_query, [lng, lat, lng, lat, radius, limit])

        return jsonify({
            'success': True,
            'places': [dict(place) for place in places],
            'count': len(places)
        })

    except Exception as e:
        logger.error(f"Error getting nearby places: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/public/<path:filename>')
def serve_static(filename):
    """تقديم الملفات الثابتة"""
    try:
        public_dir = BASE_DIR / "public"
        return send_from_directory(str(public_dir), filename)
    except Exception as e:
        logger.error(f"Error serving static file {filename}: {e}")
        return "File not found", 404

@app.route('/images/<path:filename>')
def serve_image(filename):
    """تقديم الصور"""
    try:
        # البحث في مجلدات الصور المختلفة
        image_dirs = [
            IMAGES_DIR / "places",
            IMAGES_DIR / "imported",
            IMAGES_DIR / "temp",
            Path("../public/images/places"),  # المسار القديم
            Path("E:/yemen gps/public/images/places")  # المسار المطلق
        ]

        for img_dir in image_dirs:
            if img_dir.exists() and (img_dir / filename).exists():
                return send_from_directory(str(img_dir), filename)

        # إذا لم توجد الصورة، إرجاع صورة افتراضية
        return send_from_directory(str(STATIC_DIR / "assets"), "no-image.png")

    except Exception as e:
        logger.error(f"Error serving image {filename}: {e}")
        return "Image not found", 404

@app.errorhandler(404)
def not_found(error):
    return jsonify({'success': False, 'error': 'Not found'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'success': False, 'error': 'Internal server error'}), 500

if __name__ == '__main__':
    # إنشاء المجلدات المطلوبة
    os.makedirs(IMAGES_DIR / "places", exist_ok=True)
    os.makedirs(IMAGES_DIR / "imported", exist_ok=True)
    os.makedirs(IMAGES_DIR / "temp", exist_ok=True)
    os.makedirs(STATIC_DIR / "assets", exist_ok=True)
    os.makedirs(BASE_DIR / "logs", exist_ok=True)

    # تهيئة التطبيق
    initialize()

    # تسجيل المسارات المتقدمة
    register_advanced_routes(app, advanced_apis)

    # تشغيل الخادم
    print("Starting Yemen Maps Pro Server...")
    print(f"Base directory: {BASE_DIR}")
    print(f"Images directory: {IMAGES_DIR}")
    print(f"Database: {DB_CONFIG['database']} @ {DB_CONFIG['host']}")
    print(f"Server will be available at: http://localhost:5000")
    print("Advanced APIs enabled")
    print("=" * 50)

    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        threaded=True
    )

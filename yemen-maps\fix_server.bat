@echo off
chcp 65001 >nul
echo ========================================
echo      Yemen Maps - Server Fix Tool
echo ========================================
echo.

echo 🔧 إصلاح خادم خرائط اليمن...
echo.

REM إيقاف جميع عمليات Python
echo 🛑 إيقاف الخادم الحالي...
taskkill /f /im python.exe >nul 2>&1

REM انتظار قصير
timeout /t 3 >nul

REM التحقق من وجود الملفات
echo 📁 التحقق من الملفات...
if not exist "templates\index.html" (
    echo ❌ خطأ: ملف templates\index.html غير موجود
    pause
    exit /b 1
)

if not exist "templates\google-style.html" (
    echo ❌ خطأ: ملف templates\google-style.html غير موجود
    pause
    exit /b 1
)

if not exist "server\app.py" (
    echo ❌ خطأ: ملف server\app.py غير موجود
    pause
    exit /b 1
)

echo ✅ جميع الملفات موجودة

REM التحقق من قاعدة البيانات
echo 🗄️ التحقق من قاعدة البيانات...
python -c "import psycopg2; conn = psycopg2.connect(host='localhost', database='yemen_gps', user='yemen', password='admin', port=5432); print('✅ قاعدة البيانات متصلة'); conn.close()" 2>nul
if errorlevel 1 (
    echo ❌ خطأ: لا يمكن الاتصال بقاعدة البيانات
    echo تأكد من تشغيل PostgreSQL وصحة بيانات الاتصال
    pause
    exit /b 1
)

REM تشغيل الخادم
echo 🚀 تشغيل الخادم...
echo.
echo 📍 الروابط المتاحة:
echo 🏠 الرئيسية: http://localhost:5000
echo 📱 الأساسية: http://localhost:5000/basic
echo ⚡ المتقدمة: http://localhost:5000/advanced
echo 🔑 تسجيل الدخول: http://localhost:5000/login
echo 🛠️ لوحة التحكم: http://localhost:5000/admin/advanced
echo.
echo ⏳ جاري تشغيل الخادم...

python server\app.py

echo.
echo ========================================
echo تم إيقاف الخادم
echo ========================================
pause

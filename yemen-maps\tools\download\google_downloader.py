#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Yemen Maps - Google Places Downloader
تحميل البيانات من Google Places API
"""

import os
import sys
import json
import time
import requests
import psycopg2
import psycopg2.extras
from datetime import datetime
from pathlib import Path
import argparse
from tqdm import tqdm

# إعدادات قاعدة البيانات
DB_CONFIG = {
    'host': 'localhost',
    'database': 'yemen_gps',
    'user': 'yemen',
    'password': 'admin',
    'port': 5432
}

# إعدادات Google API
GOOGLE_API_KEY = "YOUR_GOOGLE_API_KEY_HERE"  # يجب تعديل هذا
GOOGLE_PLACES_URL = "https://maps.googleapis.com/maps/api/place"

# المحافظات اليمنية مع الإحداثيات
GOVERNORATES = {
    'صنعاء': {'lat': 15.3694, 'lng': 44.1910, 'radius': 25000},
    'عدن': {'lat': 12.7797, 'lng': 45.0365, 'radius': 20000},
    'تعز': {'lat': 13.5795, 'lng': 44.0207, 'radius': 20000},
    'الحديدة': {'lat': 14.7978, 'lng': 42.9545, 'radius': 25000},
    'إب': {'lat': 13.9670, 'lng': 44.1839, 'radius': 15000},
    'ذمار': {'lat': 14.5426, 'lng': 44.4054, 'radius': 15000},
    'لحج': {'lat': 13.0582, 'lng': 44.8819, 'radius': 15000},
    'أبين': {'lat': 13.9500, 'lng': 45.3667, 'radius': 20000},
    'حضرموت': {'lat': 15.9500, 'lng': 48.6167, 'radius': 30000},
    'البيضاء': {'lat': 13.9850, 'lng': 45.5733, 'radius': 15000},
    'مأرب': {'lat': 15.4694, 'lng': 45.3222, 'radius': 20000},
    'الجوف': {'lat': 16.6000, 'lng': 45.5000, 'radius': 25000},
    'صعدة': {'lat': 16.9400, 'lng': 43.7639, 'radius': 20000},
    'حجة': {'lat': 15.6944, 'lng': 43.6056, 'radius': 20000},
    'المحويت': {'lat': 15.4700, 'lng': 43.5500, 'radius': 15000},
    'عمران': {'lat': 15.6594, 'lng': 43.9444, 'radius': 15000},
    'ريمة': {'lat': 14.6000, 'lng': 43.7000, 'radius': 10000},
    'الضالع': {'lat': 13.7000, 'lng': 44.7333, 'radius': 15000},
    'شبوة': {'lat': 14.5333, 'lng': 46.8333, 'radius': 25000},
    'المهرة': {'lat': 16.7167, 'lng': 51.8500, 'radius': 30000},
    'جزيرة سقطرى': {'lat': 12.5000, 'lng': 53.8167, 'radius': 20000}
}

# أنواع الأماكن للبحث
PLACE_TYPES = [
    'restaurant', 'hospital', 'school', 'mosque', 'bank', 
    'gas_station', 'shopping_mall', 'hotel', 'pharmacy', 
    'university', 'tourist_attraction', 'park', 'museum'
]

class GooglePlacesDownloader:
    def __init__(self, api_key=None):
        self.api_key = api_key or GOOGLE_API_KEY
        self.conn = None
        self.cursor = None
        self.downloaded_places = 0
        self.downloaded_photos = 0
        self.api_calls = 0
        self.errors = []
        
    def connect_database(self):
        """الاتصال بقاعدة البيانات"""
        try:
            self.conn = psycopg2.connect(**DB_CONFIG)
            self.cursor = self.conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            print("✅ Connected to database successfully")
            return True
        except Exception as e:
            print(f"❌ Database connection error: {e}")
            return False
    
    def search_places_nearby(self, lat, lng, radius, place_type):
        """البحث عن الأماكن القريبة"""
        try:
            url = f"{GOOGLE_PLACES_URL}/nearbysearch/json"
            params = {
                'location': f"{lat},{lng}",
                'radius': radius,
                'type': place_type,
                'key': self.api_key
            }
            
            response = requests.get(url, params=params)
            self.api_calls += 1
            
            if response.status_code == 200:
                data = response.json()
                if data['status'] == 'OK':
                    return data.get('results', [])
                else:
                    print(f"⚠️ API Error: {data['status']}")
                    return []
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"❌ Search error: {e}")
            self.errors.append(f"Search error: {e}")
            return []
    
    def get_place_details(self, place_id):
        """جلب تفاصيل المكان"""
        try:
            url = f"{GOOGLE_PLACES_URL}/details/json"
            params = {
                'place_id': place_id,
                'fields': 'name,formatted_address,geometry,rating,user_ratings_total,formatted_phone_number,website,opening_hours,photos,types',
                'key': self.api_key
            }
            
            response = requests.get(url, params=params)
            self.api_calls += 1
            
            if response.status_code == 200:
                data = response.json()
                if data['status'] == 'OK':
                    return data.get('result', {})
                else:
                    print(f"⚠️ Details API Error: {data['status']}")
                    return {}
            else:
                print(f"❌ Details HTTP Error: {response.status_code}")
                return {}
                
        except Exception as e:
            print(f"❌ Details error: {e}")
            self.errors.append(f"Details error: {e}")
            return {}
    
    def download_photo(self, photo_reference, place_id, photo_index):
        """تحميل صورة المكان"""
        try:
            url = f"{GOOGLE_PLACES_URL}/photo"
            params = {
                'photoreference': photo_reference,
                'maxwidth': 800,
                'key': self.api_key
            }
            
            response = requests.get(url, params=params)
            self.api_calls += 1
            
            if response.status_code == 200:
                # حفظ الصورة
                images_dir = Path("../../images/places")
                images_dir.mkdir(parents=True, exist_ok=True)
                
                filename = f"{place_id}_{photo_index}.jpg"
                file_path = images_dir / filename
                
                with open(file_path, 'wb') as f:
                    f.write(response.content)
                
                # حفظ في قاعدة البيانات
                self.save_photo_to_database(place_id, photo_reference, str(file_path), photo_index == 0)
                
                return str(file_path)
            else:
                print(f"❌ Photo download error: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ Photo download error: {e}")
            return None
    
    def save_place_to_database(self, place_data, governorate_id):
        """حفظ المكان في قاعدة البيانات"""
        try:
            # تحضير البيانات
            place_id = place_data.get('place_id')
            name = place_data.get('name', '')
            geometry = place_data.get('geometry', {}).get('location', {})
            lat = geometry.get('lat')
            lng = geometry.get('lng')
            
            if not lat or not lng:
                return False
            
            # تحديد الفئة من الأنواع
            types = place_data.get('types', [])
            category = self.determine_category(types)
            
            # إدراج المكان
            insert_query = """
                INSERT INTO places (
                    place_id, name, name_ar, latitude, longitude, location_point,
                    governorate_id, category, address, phone, website, rating,
                    user_ratings_total, source, is_active, is_verified, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s, ST_SetSRID(ST_MakePoint(%s, %s), 4326),
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                ) ON CONFLICT (place_id) DO UPDATE SET
                    name = EXCLUDED.name,
                    rating = EXCLUDED.rating,
                    user_ratings_total = EXCLUDED.user_ratings_total,
                    updated_at = CURRENT_TIMESTAMP
            """
            
            self.cursor.execute(insert_query, (
                place_id, name, name, lat, lng, lng, lat,
                governorate_id, category, place_data.get('formatted_address', ''),
                place_data.get('formatted_phone_number', ''), place_data.get('website', ''),
                place_data.get('rating'), place_data.get('user_ratings_total', 0),
                'google', True, True, datetime.now()
            ))
            
            self.downloaded_places += 1
            return True
            
        except Exception as e:
            print(f"❌ Save place error: {e}")
            self.errors.append(f"Save place error: {e}")
            return False
    
    def save_photo_to_database(self, place_id, photo_reference, photo_path, is_primary):
        """حفظ الصورة في قاعدة البيانات"""
        try:
            insert_query = """
                INSERT INTO place_photos (
                    place_id, photo_reference, photo_path, photo_type,
                    is_primary, source, created_at
                ) VALUES (%s, %s, %s, %s, %s, %s, %s)
            """
            
            self.cursor.execute(insert_query, (
                place_id, photo_reference, photo_path, 'general',
                is_primary, 'google', datetime.now()
            ))
            
            self.downloaded_photos += 1
            
        except Exception as e:
            print(f"❌ Save photo error: {e}")
    
    def determine_category(self, types):
        """تحديد فئة المكان من الأنواع"""
        category_mapping = {
            'restaurant': 'restaurant',
            'food': 'restaurant',
            'hospital': 'hospital',
            'health': 'hospital',
            'school': 'school',
            'university': 'university',
            'mosque': 'mosque',
            'place_of_worship': 'mosque',
            'bank': 'bank',
            'atm': 'bank',
            'gas_station': 'gas_station',
            'shopping_mall': 'shopping_mall',
            'store': 'shopping_mall',
            'lodging': 'hotel',
            'pharmacy': 'pharmacy',
            'tourist_attraction': 'tourist_attraction',
            'park': 'park'
        }
        
        for place_type in types:
            if place_type in category_mapping:
                return category_mapping[place_type]
        
        return 'general'
    
    def get_governorate_id(self, governorate_name):
        """الحصول على معرف المحافظة"""
        try:
            self.cursor.execute(
                "SELECT id FROM locations WHERE name_ar = %s AND type = 'governorate'",
                [governorate_name]
            )
            result = self.cursor.fetchone()
            return result['id'] if result else None
        except Exception as e:
            print(f"❌ Get governorate ID error: {e}")
            return None
    
    def download_governorate_data(self, governorate_name):
        """تحميل بيانات محافظة كاملة"""
        print(f"🚀 Starting download for {governorate_name}...")
        
        if governorate_name not in GOVERNORATES:
            print(f"❌ Governorate {governorate_name} not found")
            return False
        
        gov_info = GOVERNORATES[governorate_name]
        governorate_id = self.get_governorate_id(governorate_name)
        
        if not governorate_id:
            print(f"❌ Governorate ID not found for {governorate_name}")
            return False
        
        # إنشاء سجل إحصائيات التحميل
        self.cursor.execute("""
            INSERT INTO download_stats (location_id, location_name, status)
            VALUES (%s, %s, %s) RETURNING id
        """, [governorate_id, governorate_name, 'in_progress'])
        
        stats_id = self.cursor.fetchone()['id']
        self.conn.commit()
        
        total_places = 0
        
        try:
            # البحث عن كل نوع من الأماكن
            for place_type in tqdm(PLACE_TYPES, desc=f"Downloading {governorate_name}"):
                print(f"  🔍 Searching for {place_type}...")
                
                places = self.search_places_nearby(
                    gov_info['lat'], gov_info['lng'], 
                    gov_info['radius'], place_type
                )
                
                for place in places:
                    # جلب تفاصيل المكان
                    details = self.get_place_details(place['place_id'])
                    if details:
                        # حفظ المكان
                        if self.save_place_to_database(details, governorate_id):
                            total_places += 1
                            
                            # تحميل الصور (أول 3 صور فقط)
                            photos = details.get('photos', [])
                            for i, photo in enumerate(photos[:3]):
                                self.download_photo(
                                    photo['photo_reference'], 
                                    place['place_id'], i
                                )
                    
                    # توقف قصير لتجنب تجاوز حدود API
                    time.sleep(0.1)
            
            # تحديث إحصائيات التحميل
            self.cursor.execute("""
                UPDATE download_stats 
                SET end_time = %s, status = %s, places_found = %s, 
                    places_downloaded = %s, photos_downloaded = %s, api_calls_used = %s
                WHERE id = %s
            """, [
                datetime.now(), 'completed', total_places,
                self.downloaded_places, self.downloaded_photos, self.api_calls, stats_id
            ])
            
            self.conn.commit()
            
            print(f"✅ Completed {governorate_name}: {total_places} places, {self.downloaded_photos} photos")
            return True
            
        except Exception as e:
            print(f"❌ Download error for {governorate_name}: {e}")
            
            # تحديث حالة الخطأ
            self.cursor.execute("""
                UPDATE download_stats 
                SET end_time = %s, status = %s, error_message = %s
                WHERE id = %s
            """, [datetime.now(), 'failed', str(e), stats_id])
            
            self.conn.commit()
            return False

def main():
    parser = argparse.ArgumentParser(description='Download Yemen places data from Google Places API')
    parser.add_argument('--governorate', type=str, help='Governorate name to download')
    parser.add_argument('--api-key', type=str, help='Google Places API key')
    parser.add_argument('--all', action='store_true', help='Download all governorates')
    
    args = parser.parse_args()
    
    if not args.api_key:
        print("❌ Google Places API key is required")
        print("Usage: python google_downloader.py --api-key YOUR_API_KEY --governorate عدن")
        return
    
    downloader = GooglePlacesDownloader(args.api_key)
    
    if not downloader.connect_database():
        return
    
    if args.all:
        print("🚀 Starting download for all governorates...")
        for gov_name in GOVERNORATES.keys():
            downloader.download_governorate_data(gov_name)
            time.sleep(2)  # توقف بين المحافظات
    elif args.governorate:
        downloader.download_governorate_data(args.governorate)
    else:
        print("❌ Please specify --governorate or --all")
        return
    
    print(f"\n📊 Download Summary:")
    print(f"✅ Total places downloaded: {downloader.downloaded_places}")
    print(f"📸 Total photos downloaded: {downloader.downloaded_photos}")
    print(f"🔗 Total API calls used: {downloader.api_calls}")
    
    if downloader.errors:
        print(f"⚠️ Errors encountered: {len(downloader.errors)}")

if __name__ == "__main__":
    main()

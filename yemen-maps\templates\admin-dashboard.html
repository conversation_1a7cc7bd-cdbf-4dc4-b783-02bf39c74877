<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🇾🇪 Yemen Maps Pro - لوحة التحكم الإدارية</title>
    
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.css">
    
    <style>
        :root {
            --primary-color: #1a73e8;
            --secondary-color: #34a853;
            --danger-color: #ea4335;
            --warning-color: #fbbc04;
            --dark-color: #202124;
            --light-color: #f8f9fa;
            --border-radius: 8px;
            --shadow: 0 2px 10px rgba(0,0,0,0.1);
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * { 
            font-family: 'Cairo', sans-serif; 
            box-sizing: border-box;
        }
        
        body { 
            background: var(--light-color);
            margin: 0;
        }
        
        /* Sidebar */
        .sidebar {
            position: fixed; top: 0; right: 0;
            width: 280px; height: 100vh;
            background: white; box-shadow: var(--shadow);
            z-index: 1000; overflow-y: auto;
        }
        
        .sidebar-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #4285f4 100%);
            color: white; padding: 20px; text-align: center;
        }
        
        .sidebar-menu {
            padding: 20px 0;
        }
        
        .menu-item {
            display: block; padding: 12px 20px;
            color: var(--dark-color); text-decoration: none;
            transition: var(--transition);
            border-right: 3px solid transparent;
        }
        
        .menu-item:hover, .menu-item.active {
            background: #f8f9fa; color: var(--primary-color);
            border-right-color: var(--primary-color);
        }
        
        .menu-item i {
            width: 20px; margin-left: 10px;
        }
        
        /* Main Content */
        .main-content {
            margin-right: 280px; padding: 20px;
            min-height: 100vh;
        }
        
        /* Header */
        .content-header {
            background: white; padding: 20px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow); margin-bottom: 20px;
            display: flex; justify-content: space-between;
            align-items: center;
        }
        
        /* Stats Cards */
        .stats-grid {
            display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px; margin-bottom: 30px;
        }
        
        .stat-card {
            background: white; padding: 25px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow); text-align: center;
            transition: var(--transition);
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }
        
        .stat-icon {
            width: 60px; height: 60px; border-radius: 50%;
            display: flex; align-items: center; justify-content: center;
            margin: 0 auto 15px; font-size: 24px; color: white;
        }
        
        .stat-number {
            font-size: 2rem; font-weight: 700;
            color: var(--dark-color); margin-bottom: 5px;
        }
        
        .stat-label {
            color: #5f6368; font-size: 14px;
        }
        
        /* Charts */
        .chart-container {
            background: white; padding: 25px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow); margin-bottom: 20px;
        }
        
        .chart-title {
            font-size: 18px; font-weight: 600;
            margin-bottom: 20px; color: var(--dark-color);
        }
        
        /* Tables */
        .table-container {
            background: white; border-radius: var(--border-radius);
            box-shadow: var(--shadow); overflow: hidden;
        }
        
        .table-header {
            background: var(--light-color); padding: 20px;
            border-bottom: 1px solid #e8eaed;
        }
        
        .table-title {
            font-size: 18px; font-weight: 600;
            color: var(--dark-color); margin: 0;
        }
        
        .table {
            margin: 0;
        }
        
        .table th {
            background: var(--light-color);
            border: none; font-weight: 600;
            color: var(--dark-color);
        }
        
        .table td {
            border: none; border-bottom: 1px solid #f1f3f4;
            vertical-align: middle;
        }
        
        /* Buttons */
        .btn-primary {
            background: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-success {
            background: var(--secondary-color);
            border-color: var(--secondary-color);
        }
        
        .btn-danger {
            background: var(--danger-color);
            border-color: var(--danger-color);
        }
        
        .btn-warning {
            background: var(--warning-color);
            border-color: var(--warning-color);
            color: var(--dark-color);
        }
        
        /* Loading */
        .loading {
            text-align: center; padding: 40px;
            color: #5f6368;
        }
        
        .loading i {
            font-size: 24px; margin-bottom: 10px;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
                transition: var(--transition);
            }
            
            .sidebar.open {
                transform: translateX(0);
            }
            
            .main-content {
                margin-right: 0;
            }
            
            .content-header {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h4><i class="fas fa-tachometer-alt"></i> لوحة التحكم</h4>
            <p class="mb-0">Yemen Maps Pro</p>
        </div>
        
        <div class="sidebar-menu">
            <a href="#dashboard" class="menu-item active" onclick="showSection('dashboard')">
                <i class="fas fa-chart-line"></i> الإحصائيات
            </a>
            <a href="#places" class="menu-item" onclick="showSection('places')">
                <i class="fas fa-map-marker-alt"></i> إدارة الأماكن
            </a>
            <a href="#categories" class="menu-item" onclick="showSection('categories')">
                <i class="fas fa-tags"></i> الفئات
            </a>
            <a href="#users" class="menu-item" onclick="showSection('users')">
                <i class="fas fa-users"></i> المستخدمين
            </a>
            <a href="#settings" class="menu-item" onclick="showSection('settings')">
                <i class="fas fa-cog"></i> الإعدادات
            </a>
            <a href="#data" class="menu-item" onclick="showSection('data')">
                <i class="fas fa-download"></i> تحميل البيانات
            </a>
            <a href="/" class="menu-item">
                <i class="fas fa-map"></i> عرض الخريطة
            </a>
        </div>
    </div>
    
    <!-- Main Content -->
    <div class="main-content">
        <!-- Header -->
        <div class="content-header">
            <div>
                <h2 class="mb-0">مرحباً بك في لوحة التحكم</h2>
                <p class="text-muted mb-0">إدارة نظام خرائط اليمن المتقدم</p>
            </div>
            <div>
                <button class="btn btn-primary" onclick="refreshData()">
                    <i class="fas fa-sync-alt"></i> تحديث البيانات
                </button>
            </div>
        </div>
        
        <!-- Dashboard Section -->
        <div id="dashboard-section" class="content-section">
            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon" style="background: var(--primary-color);">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <div class="stat-number" id="totalPlaces">0</div>
                    <div class="stat-label">إجمالي الأماكن</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon" style="background: var(--secondary-color);">
                        <i class="fas fa-images"></i>
                    </div>
                    <div class="stat-number" id="totalPhotos">0</div>
                    <div class="stat-label">إجمالي الصور</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon" style="background: var(--warning-color);">
                        <i class="fas fa-tags"></i>
                    </div>
                    <div class="stat-number" id="totalCategories">0</div>
                    <div class="stat-label">الفئات</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon" style="background: var(--danger-color);">
                        <i class="fas fa-map"></i>
                    </div>
                    <div class="stat-number" id="totalGovernorates">0</div>
                    <div class="stat-label">المحافظات</div>
                </div>
            </div>
            
            <!-- Charts -->
            <div class="row">
                <div class="col-md-6">
                    <div class="chart-container">
                        <h5 class="chart-title">الأماكن حسب الفئة</h5>
                        <canvas id="categoriesChart"></canvas>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="chart-container">
                        <h5 class="chart-title">الأماكن حسب المحافظة</h5>
                        <canvas id="governoratesChart"></canvas>
                    </div>
                </div>
            </div>
            
            <!-- Recent Places Table -->
            <div class="table-container">
                <div class="table-header">
                    <h5 class="table-title">الأماكن المضافة حديثاً</h5>
                </div>
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>الفئة</th>
                                <th>المحافظة</th>
                                <th>التقييم</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="recentPlacesTable">
                            <tr>
                                <td colspan="5" class="text-center">
                                    <div class="loading">
                                        <i class="fas fa-spinner"></i>
                                        <p>جاري تحميل البيانات...</p>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <!-- Other sections will be added here -->
        <div id="places-section" class="content-section" style="display: none;">
            <h3>إدارة الأماكن</h3>
            <p>قريباً...</p>
        </div>
        
        <div id="categories-section" class="content-section" style="display: none;">
            <h3>إدارة الفئات</h3>
            <p>قريباً...</p>
        </div>
        
        <div id="users-section" class="content-section" style="display: none;">
            <h3>إدارة المستخدمين</h3>
            <p>قريباً...</p>
        </div>
        
        <div id="settings-section" class="content-section" style="display: none;">
            <h3>الإعدادات</h3>
            <p>قريباً...</p>
        </div>
        
        <div id="data-section" class="content-section" style="display: none;">
            <h3>تحميل البيانات</h3>
            <p>قريباً...</p>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
    
    <script>
        let categoriesChart, governoratesChart;
        
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            loadDashboardData();
            initCharts();
        });
        
        // Load dashboard data
        async function loadDashboardData() {
            try {
                const response = await fetch('/api/stats');
                const data = await response.json();
                
                if (data.success) {
                    updateStats(data.stats);
                    updateCharts(data.stats);
                    loadRecentPlaces();
                }
            } catch (error) {
                console.error('Error loading dashboard data:', error);
            }
        }
        
        // Update stats cards
        function updateStats(stats) {
            document.getElementById('totalPlaces').textContent = stats.total_places || 0;
            document.getElementById('totalPhotos').textContent = stats.total_photos || 0;
            document.getElementById('totalCategories').textContent = stats.categories?.length || 0;
            document.getElementById('totalGovernorates').textContent = stats.governorates?.length || 0;
        }
        
        // Initialize charts
        function initCharts() {
            const ctx1 = document.getElementById('categoriesChart').getContext('2d');
            categoriesChart = new Chart(ctx1, {
                type: 'doughnut',
                data: {
                    labels: [],
                    datasets: [{
                        data: [],
                        backgroundColor: [
                            '#1a73e8', '#34a853', '#ea4335', '#fbbc04',
                            '#9aa0a6', '#8ab4f8', '#81c995', '#f28b82'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
            
            const ctx2 = document.getElementById('governoratesChart').getContext('2d');
            governoratesChart = new Chart(ctx2, {
                type: 'bar',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'عدد الأماكن',
                        data: [],
                        backgroundColor: '#1a73e8'
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }
        
        // Update charts
        function updateCharts(stats) {
            if (stats.categories) {
                categoriesChart.data.labels = stats.categories.map(c => c.category);
                categoriesChart.data.datasets[0].data = stats.categories.map(c => c.count);
                categoriesChart.update();
            }
            
            if (stats.governorates) {
                governoratesChart.data.labels = stats.governorates.map(g => g.name_ar);
                governoratesChart.data.datasets[0].data = stats.governorates.map(g => g.count);
                governoratesChart.update();
            }
        }
        
        // Load recent places
        async function loadRecentPlaces() {
            try {
                const response = await fetch('/api/places?limit=10');
                const data = await response.json();
                
                if (data.success) {
                    const tbody = document.getElementById('recentPlacesTable');
                    tbody.innerHTML = '';
                    
                    data.places.forEach(place => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${place.name || place.name_ar}</td>
                            <td><span class="badge bg-primary">${getCategoryName(place.category)}</span></td>
                            <td>${place.governorate_name || 'غير محدد'}</td>
                            <td>${place.rating ? '★'.repeat(Math.floor(place.rating)) + ' ' + place.rating : 'لا يوجد'}</td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary" onclick="viewPlace('${place.place_id}')">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-success" onclick="editPlace('${place.place_id}')">
                                    <i class="fas fa-edit"></i>
                                </button>
                            </td>
                        `;
                        tbody.appendChild(row);
                    });
                }
            } catch (error) {
                console.error('Error loading recent places:', error);
            }
        }
        
        // Helper functions
        function getCategoryName(category) {
            const categories = {
                'restaurant': 'مطعم',
                'hospital': 'مستشفى',
                'school': 'مدرسة',
                'mosque': 'مسجد',
                'bank': 'بنك',
                'gas_station': 'محطة وقود',
                'shopping_mall': 'مركز تجاري',
                'hotel': 'فندق',
                'general': 'عام'
            };
            return categories[category] || category || 'غير محدد';
        }
        
        function showSection(sectionName) {
            // Hide all sections
            document.querySelectorAll('.content-section').forEach(section => {
                section.style.display = 'none';
            });
            
            // Show selected section
            document.getElementById(sectionName + '-section').style.display = 'block';
            
            // Update menu active state
            document.querySelectorAll('.menu-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');
        }
        
        function refreshData() {
            loadDashboardData();
        }
        
        function viewPlace(placeId) {
            window.open(`/?place=${placeId}`, '_blank');
        }
        
        function editPlace(placeId) {
            alert('تحرير المكان: ' + placeId);
        }
    </script>
</body>
</html>

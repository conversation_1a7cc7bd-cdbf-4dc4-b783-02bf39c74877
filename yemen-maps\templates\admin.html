<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🛠️ لوحة إدارة خرائط اليمن</title>
    
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * { font-family: 'Cairo', sans-serif; }
        body { background: #f8f9fa; }
        
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .sidebar {
            background: white;
            min-height: calc(100vh - 76px);
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            padding: 20px 0;
        }
        
        .sidebar .nav-link {
            color: #495057;
            padding: 12px 20px;
            border-radius: 0;
            transition: all 0.3s;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background: #667eea;
            color: white;
        }
        
        .main-content {
            padding: 20px;
        }
        
        .stats-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            transition: transform 0.3s;
        }
        
        .stats-card:hover {
            transform: translateY(-2px);
        }
        
        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #667eea;
        }
        
        .stats-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .governorate-card {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s;
        }
        
        .governorate-card:hover {
            transform: translateY(-2px);
        }
        
        .progress-bar {
            height: 8px;
            border-radius: 4px;
            background: #e9ecef;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress {
            height: 100%;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            transition: width 0.3s;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .status-pending { background: #ffeaa7; color: #d63031; }
        .status-partial { background: #fab1a0; color: #e17055; }
        .status-complete { background: #00b894; color: white; }
        .status-in_progress { background: #74b9ff; color: white; }
        
        .btn-download {
            background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.8rem;
            transition: all 0.3s;
        }
        
        .btn-download:hover {
            transform: scale(1.05);
            color: white;
        }
        
        .table-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-map-marked-alt"></i> خرائط اليمن - لوحة الإدارة
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">
                    <i class="fas fa-home"></i> العودة للخريطة
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link active" href="#dashboard" onclick="showSection('dashboard')">
                            <i class="fas fa-tachometer-alt"></i> لوحة المعلومات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#governorates" onclick="showSection('governorates')">
                            <i class="fas fa-map"></i> المحافظات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#places" onclick="showSection('places')">
                            <i class="fas fa-map-marker-alt"></i> الأماكن
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#download" onclick="showSection('download')">
                            <i class="fas fa-download"></i> تحميل البيانات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#settings" onclick="showSection('settings')">
                            <i class="fas fa-cog"></i> الإعدادات
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <!-- Dashboard Section -->
                <div id="dashboard" class="content-section">
                    <h2><i class="fas fa-tachometer-alt"></i> لوحة المعلومات</h2>
                    
                    <div class="row">
                        <div class="col-md-3">
                            <div class="stats-card text-center">
                                <div class="stats-number" id="totalPlacesAdmin">0</div>
                                <div class="stats-label">إجمالي الأماكن</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card text-center">
                                <div class="stats-number" id="totalPhotosAdmin">0</div>
                                <div class="stats-label">إجمالي الصور</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card text-center">
                                <div class="stats-number" id="totalGovernoratesAdmin">22</div>
                                <div class="stats-label">المحافظات</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card text-center">
                                <div class="stats-number" id="completionPercentage">0%</div>
                                <div class="stats-label">نسبة الإكمال</div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="table-container">
                                <h5>الفئات الأكثر</h5>
                                <div id="categoriesChart">
                                    <div class="loading">
                                        <i class="fas fa-spinner fa-spin"></i>
                                        <p>جاري تحميل البيانات...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="table-container">
                                <h5>المحافظات الأكثر</h5>
                                <div id="governoratesChart">
                                    <div class="loading">
                                        <i class="fas fa-spinner fa-spin"></i>
                                        <p>جاري تحميل البيانات...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Governorates Section -->
                <div id="governorates" class="content-section" style="display: none;">
                    <h2><i class="fas fa-map"></i> إدارة المحافظات</h2>
                    
                    <div id="governoratesList">
                        <div class="loading">
                            <i class="fas fa-spinner fa-spin"></i>
                            <p>جاري تحميل المحافظات...</p>
                        </div>
                    </div>
                </div>

                <!-- Places Section -->
                <div id="places" class="content-section" style="display: none;">
                    <h2><i class="fas fa-map-marker-alt"></i> إدارة الأماكن</h2>
                    
                    <div class="table-container">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <input type="text" class="form-control" id="searchPlaces" placeholder="البحث في الأماكن...">
                            </div>
                            <div>
                                <select class="form-select" id="filterCategory">
                                    <option value="">جميع الفئات</option>
                                </select>
                            </div>
                        </div>
                        
                        <div id="placesTable">
                            <div class="loading">
                                <i class="fas fa-spinner fa-spin"></i>
                                <p>جاري تحميل الأماكن...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Download Section -->
                <div id="download" class="content-section" style="display: none;">
                    <h2><i class="fas fa-download"></i> تحميل البيانات من Google</h2>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>ملاحظة:</strong> تحميل البيانات من Google Places API يتطلب مفتاح API صالح.
                    </div>
                    
                    <div class="table-container">
                        <h5>حالة التحميل للمحافظات</h5>
                        <div id="downloadStatus">
                            <div class="loading">
                                <i class="fas fa-spinner fa-spin"></i>
                                <p>جاري تحميل حالة التحميل...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Settings Section -->
                <div id="settings" class="content-section" style="display: none;">
                    <h2><i class="fas fa-cog"></i> الإعدادات</h2>
                    
                    <div class="table-container">
                        <h5>إعدادات النظام</h5>
                        
                        <div class="mb-3">
                            <label class="form-label">مفتاح Google Places API</label>
                            <input type="password" class="form-control" id="googleApiKey" placeholder="أدخل مفتاح API">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">عدد الأماكن لكل طلب</label>
                            <input type="number" class="form-control" id="placesPerRequest" value="20" min="1" max="60">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">نطاق البحث (كيلومتر)</label>
                            <input type="number" class="form-control" id="searchRadius" value="5" min="1" max="50">
                        </div>
                        
                        <button class="btn btn-primary" onclick="saveSettings()">
                            <i class="fas fa-save"></i> حفظ الإعدادات
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let currentStats = {};
        let currentGovernorates = [];
        let currentPlaces = [];

        // Initialize admin panel
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
            loadGovernorates();
            loadCategories();
        });

        // Show section
        function showSection(sectionId) {
            // Hide all sections
            document.querySelectorAll('.content-section').forEach(section => {
                section.style.display = 'none';
            });
            
            // Remove active class from all nav links
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });
            
            // Show selected section
            document.getElementById(sectionId).style.display = 'block';
            
            // Add active class to clicked nav link
            event.target.classList.add('active');
            
            // Load section-specific data
            if (sectionId === 'places') {
                loadPlaces();
            } else if (sectionId === 'download') {
                loadDownloadStatus();
            }
        }

        // Load statistics
        async function loadStats() {
            try {
                const response = await fetch('/api/stats');
                const data = await response.json();
                
                if (data.success) {
                    currentStats = data.stats;
                    updateStatsDisplay();
                    updateChartsDisplay();
                }
            } catch (error) {
                console.error('Error loading stats:', error);
            }
        }

        // Update stats display
        function updateStatsDisplay() {
            document.getElementById('totalPlacesAdmin').textContent = currentStats.total_places || 0;
            document.getElementById('totalPhotosAdmin').textContent = currentStats.total_photos || 0;
            
            // Calculate completion percentage
            const totalExpected = 22 * 100; // Assuming 100 places per governorate as target
            const completionPerc = Math.round((currentStats.total_places / totalExpected) * 100);
            document.getElementById('completionPercentage').textContent = completionPerc + '%';
        }

        // Update charts display
        function updateChartsDisplay() {
            // Categories chart
            const categoriesHtml = currentStats.categories.map(cat => `
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>${getCategoryNameAr(cat.category)}</span>
                    <span class="badge bg-primary">${cat.count}</span>
                </div>
            `).join('');
            document.getElementById('categoriesChart').innerHTML = categoriesHtml;

            // Governorates chart
            const governoratesHtml = currentStats.governorates.map(gov => `
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>${gov.name_ar}</span>
                    <span class="badge bg-success">${gov.count}</span>
                </div>
            `).join('');
            document.getElementById('governoratesChart').innerHTML = governoratesHtml;
        }

        // Load governorates
        async function loadGovernorates() {
            try {
                const response = await fetch('/api/governorates');
                const data = await response.json();
                
                if (data.success) {
                    currentGovernorates = data.governorates;
                    updateGovernoratesDisplay();
                }
            } catch (error) {
                console.error('Error loading governorates:', error);
            }
        }

        // Update governorates display
        function updateGovernoratesDisplay() {
            const governoratesHtml = currentGovernorates.map(gov => {
                const progress = Math.min((gov.actual_places_count / 100) * 100, 100);
                const statusClass = getStatusClass(gov.data_status);
                
                return `
                    <div class="governorate-card">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-1">${gov.name_ar}</h6>
                            <span class="status-badge ${statusClass}">${getStatusText(gov.data_status)}</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress" style="width: ${progress}%"></div>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mt-2">
                            <small class="text-muted">${gov.actual_places_count} مكان</small>
                            <button class="btn btn-download btn-sm" onclick="startDownload('${gov.name_ar}')">
                                <i class="fas fa-download"></i> تحميل
                            </button>
                        </div>
                    </div>
                `;
            }).join('');
            
            document.getElementById('governoratesList').innerHTML = governoratesHtml;
        }

        // Load places
        async function loadPlaces() {
            try {
                const response = await fetch('/api/places?limit=1000');
                const data = await response.json();
                
                if (data.success) {
                    currentPlaces = data.places;
                    updatePlacesDisplay();
                }
            } catch (error) {
                console.error('Error loading places:', error);
            }
        }

        // Update places display
        function updatePlacesDisplay() {
            const placesHtml = `
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>الفئة</th>
                            <th>المحافظة</th>
                            <th>التقييم</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${currentPlaces.slice(0, 50).map(place => `
                            <tr>
                                <td>${place.name || place.name_ar}</td>
                                <td>${getCategoryNameAr(place.category)}</td>
                                <td>${place.governorate_name || 'غير محدد'}</td>
                                <td>${place.rating ? '★'.repeat(Math.floor(place.rating)) + ' ' + place.rating : 'غير مقيم'}</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary" onclick="editPlace('${place.place_id}')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
            
            document.getElementById('placesTable').innerHTML = placesHtml;
        }

        // Load categories for filter
        async function loadCategories() {
            try {
                const response = await fetch('/api/categories');
                const data = await response.json();
                
                if (data.success) {
                    const categoriesHtml = data.categories.map(cat => 
                        `<option value="${cat.name}">${cat.name_ar}</option>`
                    ).join('');
                    document.getElementById('filterCategory').innerHTML = 
                        '<option value="">جميع الفئات</option>' + categoriesHtml;
                }
            } catch (error) {
                console.error('Error loading categories:', error);
            }
        }

        // Load download status
        function loadDownloadStatus() {
            const statusHtml = currentGovernorates.map(gov => {
                const statusClass = getStatusClass(gov.data_status);
                
                return `
                    <div class="governorate-card">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">${gov.name_ar}</h6>
                                <small class="text-muted">${gov.actual_places_count} مكان محفوظ</small>
                            </div>
                            <div class="text-end">
                                <span class="status-badge ${statusClass}">${getStatusText(gov.data_status)}</span>
                                <br>
                                <button class="btn btn-download btn-sm mt-1" onclick="startDownload('${gov.name_ar}')">
                                    <i class="fas fa-download"></i> بدء التحميل
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
            
            document.getElementById('downloadStatus').innerHTML = statusHtml;
        }

        // Helper functions
        function getCategoryNameAr(category) {
            const categories = {
                'restaurant': 'مطعم', 'hospital': 'مستشفى', 'school': 'مدرسة',
                'mosque': 'مسجد', 'bank': 'بنك', 'gas_station': 'محطة وقود',
                'shopping_mall': 'مركز تجاري', 'hotel': 'فندق',
                'pharmacy': 'صيدلية', 'university': 'جامعة', 'general': 'عام'
            };
            return categories[category] || category;
        }

        function getStatusClass(status) {
            const classes = {
                'pending': 'status-pending',
                'partial': 'status-partial', 
                'complete': 'status-complete',
                'in_progress': 'status-in_progress'
            };
            return classes[status] || 'status-pending';
        }

        function getStatusText(status) {
            const texts = {
                'pending': 'في الانتظار',
                'partial': 'جزئي',
                'complete': 'مكتمل', 
                'in_progress': 'قيد التنفيذ'
            };
            return texts[status] || 'غير محدد';
        }

        // Action functions
        function startDownload(governorate) {
            alert(`سيتم بدء تحميل بيانات ${governorate} من Google Places API`);
            // Here you would implement the actual download functionality
        }

        function editPlace(placeId) {
            alert(`تحرير المكان: ${placeId}`);
            // Here you would implement place editing functionality
        }

        function saveSettings() {
            const apiKey = document.getElementById('googleApiKey').value;
            const placesPerRequest = document.getElementById('placesPerRequest').value;
            const searchRadius = document.getElementById('searchRadius').value;
            
            // Here you would save the settings
            alert('تم حفظ الإعدادات بنجاح');
        }
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>خرائط اليمن - نمط Google Maps</title>
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <!-- Leaflet Routing Machine CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet-routing-machine@3.2.12/dist/leaflet-routing-machine.css" />
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Roboto', 'Arial', sans-serif;
            overflow: hidden;
            background: #f5f5f5;
        }
        
        /* Google Maps Style Header */
        .google-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 64px;
            background: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            z-index: 1000;
            display: flex;
            align-items: center;
            padding: 0 16px;
        }
        
        .google-logo {
            display: flex;
            align-items: center;
            margin-left: 24px;
        }
        
        .google-logo img {
            width: 32px;
            height: 32px;
            margin-left: 8px;
        }
        
        .google-logo span {
            font-size: 20px;
            font-weight: 400;
            color: #5f6368;
        }
        
        /* Google Style Search Box */
        .google-search-container {
            flex: 1;
            max-width: 400px;
            margin: 0 24px;
            position: relative;
        }
        
        .google-search-box {
            width: 100%;
            height: 48px;
            border: 1px solid #dadce0;
            border-radius: 24px;
            padding: 0 20px 0 48px;
            font-size: 16px;
            outline: none;
            transition: all 0.2s;
            background: #fff;
        }
        
        .google-search-box:focus {
            border-color: #4285f4;
            box-shadow: 0 2px 8px rgba(66,133,244,0.3);
        }
        
        .search-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #9aa0a6;
            font-size: 18px;
        }
        
        .search-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            max-height: 300px;
            overflow-y: auto;
            z-index: 1001;
            display: none;
        }
        
        .suggestion-item {
            padding: 12px 16px;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
        }
        
        .suggestion-item:hover {
            background: #f8f9fa;
        }
        
        .suggestion-icon {
            margin-left: 12px;
            color: #9aa0a6;
        }
        
        /* Google Style Controls */
        .google-controls {
            display: flex;
            align-items: center;
            gap: 16px;
        }
        
        .control-button {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 50%;
            background: #fff;
            color: #5f6368;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
            box-shadow: 0 1px 3px rgba(0,0,0,0.3);
        }
        
        .control-button:hover {
            background: #f8f9fa;
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        }
        
        /* Map Container */
        .map-container {
            position: fixed;
            top: 64px;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 1;
        }
        
        #map {
            width: 100%;
            height: 100%;
        }
        
        /* Google Style Sidebar */
        .google-sidebar {
            position: fixed;
            top: 64px;
            right: -400px;
            width: 400px;
            height: calc(100vh - 64px);
            background: #fff;
            box-shadow: -2px 0 8px rgba(0,0,0,0.15);
            z-index: 1002;
            transition: right 0.3s ease;
            overflow-y: auto;
        }
        
        .google-sidebar.open {
            right: 0;
        }
        
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #e8eaed;
            background: #f8f9fa;
        }
        
        .sidebar-content {
            padding: 20px;
        }
        
        /* Google Style Place Card */
        .place-card {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 16px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .place-card:hover {
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }
        
        .place-image {
            width: 100%;
            height: 120px;
            object-fit: cover;
            background: linear-gradient(45deg, #667eea, #764ba2);
        }
        
        .place-info {
            padding: 16px;
        }
        
        .place-name {
            font-size: 16px;
            font-weight: 500;
            color: #202124;
            margin-bottom: 4px;
        }
        
        .place-category {
            font-size: 14px;
            color: #5f6368;
            margin-bottom: 8px;
        }
        
        .place-rating {
            display: flex;
            align-items: center;
            gap: 4px;
            margin-bottom: 8px;
        }
        
        .rating-stars {
            color: #fbbc04;
        }
        
        .rating-text {
            font-size: 14px;
            color: #5f6368;
        }
        
        .place-address {
            font-size: 14px;
            color: #5f6368;
            margin-bottom: 12px;
        }
        
        .place-actions {
            display: flex;
            gap: 8px;
        }
        
        .action-button {
            flex: 1;
            padding: 8px 16px;
            border: 1px solid #dadce0;
            border-radius: 4px;
            background: #fff;
            color: #1a73e8;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .action-button:hover {
            background: #f8f9fa;
        }
        
        .action-button.primary {
            background: #1a73e8;
            color: #fff;
            border-color: #1a73e8;
        }
        
        .action-button.primary:hover {
            background: #1557b0;
        }
        
        /* Google Style Map Controls */
        .map-controls {
            position: fixed;
            bottom: 24px;
            left: 24px;
            z-index: 1003;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .map-type-selector {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        
        .map-type-button {
            display: block;
            width: 100%;
            padding: 12px 16px;
            border: none;
            background: #fff;
            color: #5f6368;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
            text-align: right;
        }
        
        .map-type-button:hover {
            background: #f8f9fa;
        }
        
        .map-type-button.active {
            background: #e8f0fe;
            color: #1a73e8;
            font-weight: 500;
        }
        
        /* Google Style Zoom Controls */
        .zoom-controls {
            position: fixed;
            bottom: 120px;
            left: 24px;
            z-index: 1003;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        
        .zoom-button {
            display: block;
            width: 48px;
            height: 48px;
            border: none;
            background: #fff;
            color: #5f6368;
            font-size: 18px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .zoom-button:hover {
            background: #f8f9fa;
        }
        
        .zoom-button:first-child {
            border-bottom: 1px solid #e8eaed;
        }
        
        /* Google Style My Location Button */
        .my-location-button {
            position: fixed;
            bottom: 24px;
            right: 24px;
            width: 48px;
            height: 48px;
            border: none;
            border-radius: 50%;
            background: #fff;
            color: #5f6368;
            font-size: 18px;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
            transition: all 0.2s;
            z-index: 1003;
        }
        
        .my-location-button:hover {
            background: #f8f9fa;
            box-shadow: 0 4px 16px rgba(0,0,0,0.3);
        }
        
        /* Loading Spinner */
        .loading-spinner {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 2000;
            background: rgba(255,255,255,0.9);
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.2);
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4285f4;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .google-header {
                padding: 0 8px;
            }
            
            .google-search-container {
                max-width: none;
                margin: 0 8px;
            }
            
            .google-sidebar {
                width: 100%;
                right: -100%;
            }
            
            .map-controls,
            .zoom-controls {
                left: 8px;
            }
            
            .my-location-button {
                right: 8px;
            }
        }
        
        /* Custom Leaflet Styles */
        .leaflet-popup-content-wrapper {
            border-radius: 8px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.2);
        }
        
        .leaflet-popup-content {
            margin: 16px;
            font-family: 'Roboto', sans-serif;
        }
        
        .leaflet-control-zoom {
            display: none;
        }
        
        .leaflet-control-attribution {
            background: rgba(255,255,255,0.8);
            font-size: 11px;
        }
    </style>
</head>
<body>
    <!-- Google Style Header -->
    <div class="google-header">
        <div class="google-logo">
            <i class="fas fa-map-marked-alt" style="color: #4285f4; font-size: 24px;"></i>
            <span>خرائط اليمن</span>
        </div>
        
        <div class="google-search-container">
            <i class="fas fa-search search-icon"></i>
            <input type="text" class="google-search-box" placeholder="ابحث عن الأماكن في اليمن..." id="searchInput">
            <div class="search-suggestions" id="searchSuggestions"></div>
        </div>
        
        <div class="google-controls">
            <button class="control-button" onclick="toggleSidebar()" title="القائمة">
                <i class="fas fa-bars"></i>
            </button>
            <button class="control-button" onclick="showDirections()" title="الاتجاهات">
                <i class="fas fa-directions"></i>
            </button>
            <button class="control-button" onclick="showNearby()" title="الأماكن القريبة">
                <i class="fas fa-map-marker-alt"></i>
            </button>
        </div>
    </div>

    <!-- Map Container -->
    <div class="map-container">
        <div id="map"></div>
    </div>

    <!-- Google Style Sidebar -->
    <div class="google-sidebar" id="sidebar">
        <div class="sidebar-header">
            <h5><i class="fas fa-list"></i> الأماكن القريبة</h5>
        </div>
        <div class="sidebar-content" id="sidebarContent">
            <!-- Places will be loaded here -->
        </div>
    </div>

    <!-- Map Controls -->
    <div class="map-controls">
        <div class="map-type-selector">
            <button class="map-type-button active" onclick="changeMapType('streets')">
                <i class="fas fa-road"></i> شوارع
            </button>
            <button class="map-type-button" onclick="changeMapType('satellite')">
                <i class="fas fa-satellite"></i> قمر صناعي
            </button>
            <button class="map-type-button" onclick="changeMapType('terrain')">
                <i class="fas fa-mountain"></i> تضاريس
            </button>
        </div>
    </div>

    <!-- Zoom Controls -->
    <div class="zoom-controls">
        <button class="zoom-button" onclick="zoomIn()">
            <i class="fas fa-plus"></i>
        </button>
        <button class="zoom-button" onclick="zoomOut()">
            <i class="fas fa-minus"></i>
        </button>
    </div>

    <!-- My Location Button -->
    <button class="my-location-button" onclick="goToMyLocation()" title="موقعي">
        <i class="fas fa-crosshairs"></i>
    </button>

    <!-- Loading Spinner -->
    <div class="loading-spinner" id="loadingSpinner">
        <div class="spinner"></div>
        <p style="margin-top: 10px; text-align: center;">جاري التحميل...</p>
    </div>

    <!-- Scripts -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="https://unpkg.com/leaflet-routing-machine@3.2.12/dist/leaflet-routing-machine.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script src="/public/js/google-maps-style.js"></script>
</body>
</html>

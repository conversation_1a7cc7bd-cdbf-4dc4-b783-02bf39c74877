/**
 * Yemen Maps - Google Maps Style Interface
 * واجهة خرائط اليمن بنمط Google Maps
 */

class GoogleStyleYemenMaps {
    constructor() {
        this.map = null;
        this.markers = [];
        this.currentLayer = null;
        this.userLocation = null;
        this.routingControl = null;
        this.searchTimeout = null;
        this.places = [];
        this.currentMapType = 'streets';
        
        this.init();
    }

    async init() {
        try {
            await this.initMap();
            await this.loadPlaces();
            this.setupEventListeners();
            this.hideLoading();
            this.getCurrentLocation();
        } catch (error) {
            console.error('Error initializing map:', error);
            this.showError('حدث خطأ في تحميل الخريطة');
        }
    }

    async initMap() {
        // إنشاء الخريطة مع إعدادات Google Maps
        this.map = L.map('map', {
            center: [15.3694, 44.1910], // صنعاء
            zoom: 12,
            zoomControl: false,
            attributionControl: true
        });

        // إضافة طبقة الخريطة الافتراضية
        this.changeMapType('streets');

        // إعداد أحداث الخريطة
        this.map.on('moveend', () => {
            this.loadNearbyPlaces();
        });

        this.map.on('click', (e) => {
            this.onMapClick(e);
        });
    }

    changeMapType(type) {
        // إزالة الطبقة الحالية
        if (this.currentLayer) {
            this.map.removeLayer(this.currentLayer);
        }

        // إضافة الطبقة الجديدة
        switch (type) {
            case 'streets':
                this.currentLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenStreetMap contributors',
                    maxZoom: 19
                });
                break;
            case 'satellite':
                this.currentLayer = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                    attribution: '© Esri',
                    maxZoom: 19
                });
                break;
            case 'terrain':
                this.currentLayer = L.tileLayer('https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenTopoMap',
                    maxZoom: 17
                });
                break;
        }

        this.currentLayer.addTo(this.map);
        this.currentMapType = type;

        // تحديث أزرار نوع الخريطة
        document.querySelectorAll('.map-type-button').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[onclick="changeMapType('${type}')"]`).classList.add('active');
    }

    async loadPlaces() {
        try {
            const response = await fetch('/api/places?limit=1000');
            const data = await response.json();
            
            if (data.success) {
                this.places = data.places;
                this.displayPlacesOnMap();
            }
        } catch (error) {
            console.error('Error loading places:', error);
        }
    }

    displayPlacesOnMap() {
        // مسح العلامات الحالية
        this.clearMarkers();

        // إضافة علامات جديدة
        this.places.forEach(place => {
            const marker = this.createGoogleStyleMarker(place);
            this.markers.push(marker);
        });
    }

    createGoogleStyleMarker(place) {
        // إنشاء أيقونة مخصصة تشبه Google Maps
        const icon = L.divIcon({
            className: 'google-marker',
            html: `
                <div style="
                    width: 32px;
                    height: 32px;
                    background: #ea4335;
                    border-radius: 50% 50% 50% 0;
                    transform: rotate(-45deg);
                    border: 2px solid #fff;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                ">
                    <i class="fas fa-map-marker-alt" style="
                        color: white;
                        font-size: 14px;
                        transform: rotate(45deg);
                    "></i>
                </div>
            `,
            iconSize: [32, 32],
            iconAnchor: [16, 32]
        });

        const marker = L.marker([place.latitude, place.longitude], { icon })
            .addTo(this.map);

        // إضافة popup بنمط Google Maps
        const popupContent = this.createGoogleStylePopup(place);
        marker.bindPopup(popupContent, {
            maxWidth: 300,
            className: 'google-popup'
        });

        // إضافة حدث النقر
        marker.on('click', () => {
            this.showPlaceDetails(place);
        });

        return marker;
    }

    createGoogleStylePopup(place) {
        return `
            <div style="font-family: Roboto, sans-serif;">
                <div style="
                    width: 100%;
                    height: 120px;
                    background: linear-gradient(45deg, #667eea, #764ba2);
                    border-radius: 8px;
                    margin-bottom: 12px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 24px;
                ">
                    <i class="fas fa-image"></i>
                </div>
                
                <h6 style="margin: 0 0 8px 0; font-weight: 500; color: #202124;">
                    ${place.name_ar || place.name_en}
                </h6>
                
                <p style="margin: 0 0 8px 0; font-size: 14px; color: #5f6368;">
                    ${this.getCategoryName(place.category_id)}
                </p>
                
                ${place.rating ? `
                    <div style="display: flex; align-items: center; gap: 4px; margin-bottom: 8px;">
                        <span style="color: #fbbc04;">★★★★☆</span>
                        <span style="font-size: 14px; color: #5f6368;">${place.rating}</span>
                    </div>
                ` : ''}
                
                <div style="display: flex; gap: 8px; margin-top: 12px;">
                    <button onclick="yemenMaps.getDirections(${place.latitude}, ${place.longitude})" 
                            style="
                                flex: 1;
                                padding: 8px 12px;
                                background: #1a73e8;
                                color: white;
                                border: none;
                                border-radius: 4px;
                                font-size: 14px;
                                cursor: pointer;
                            ">
                        <i class="fas fa-directions"></i> اتجاهات
                    </button>
                    <button onclick="yemenMaps.showPlaceDetails(${place.id})" 
                            style="
                                flex: 1;
                                padding: 8px 12px;
                                background: #fff;
                                color: #1a73e8;
                                border: 1px solid #dadce0;
                                border-radius: 4px;
                                font-size: 14px;
                                cursor: pointer;
                            ">
                        <i class="fas fa-info-circle"></i> تفاصيل
                    </button>
                </div>
            </div>
        `;
    }

    setupEventListeners() {
        // البحث
        const searchInput = document.getElementById('searchInput');
        searchInput.addEventListener('input', (e) => {
            clearTimeout(this.searchTimeout);
            this.searchTimeout = setTimeout(() => {
                this.performSearch(e.target.value);
            }, 300);
        });

        // إخفاء الاقتراحات عند النقر خارجها
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.google-search-container')) {
                this.hideSuggestions();
            }
        });
    }

    async performSearch(query) {
        if (query.length < 2) {
            this.hideSuggestions();
            return;
        }

        try {
            const response = await fetch(`/api/search?q=${encodeURIComponent(query)}&limit=5`);
            const data = await response.json();
            
            if (data.success) {
                this.showSuggestions(data.places);
            }
        } catch (error) {
            console.error('Search error:', error);
        }
    }

    showSuggestions(places) {
        const suggestionsContainer = document.getElementById('searchSuggestions');
        
        if (places.length === 0) {
            this.hideSuggestions();
            return;
        }

        const suggestionsHTML = places.map(place => `
            <div class="suggestion-item" onclick="yemenMaps.selectPlace(${place.id})">
                <i class="fas fa-map-marker-alt suggestion-icon"></i>
                <div>
                    <div style="font-weight: 500;">${place.name_ar || place.name_en}</div>
                    <div style="font-size: 12px; color: #5f6368;">
                        ${this.getCategoryName(place.category_id)} • ${place.governorate_name || 'اليمن'}
                    </div>
                </div>
            </div>
        `).join('');

        suggestionsContainer.innerHTML = suggestionsHTML;
        suggestionsContainer.style.display = 'block';
    }

    hideSuggestions() {
        document.getElementById('searchSuggestions').style.display = 'none';
    }

    selectPlace(placeId) {
        const place = this.places.find(p => p.id === placeId);
        if (place) {
            this.map.setView([place.latitude, place.longitude], 16);
            this.showPlaceDetails(place);
            document.getElementById('searchInput').value = place.name_ar || place.name_en;
        }
        this.hideSuggestions();
    }

    showPlaceDetails(place) {
        const sidebarContent = document.getElementById('sidebarContent');
        
        sidebarContent.innerHTML = `
            <div class="place-card" style="margin-bottom: 0;">
                <div class="place-image" style="
                    background: linear-gradient(45deg, #667eea, #764ba2);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 48px;
                ">
                    <i class="fas fa-image"></i>
                </div>
                <div class="place-info">
                    <div class="place-name">${place.name_ar || place.name_en}</div>
                    <div class="place-category">${this.getCategoryName(place.category_id)}</div>
                    
                    ${place.rating ? `
                        <div class="place-rating">
                            <span class="rating-stars">★★★★☆</span>
                            <span class="rating-text">${place.rating} (تقييم)</span>
                        </div>
                    ` : ''}
                    
                    <div class="place-address">
                        <i class="fas fa-map-marker-alt"></i>
                        ${place.governorate_name || 'اليمن'}
                    </div>
                    
                    <div class="place-actions">
                        <button class="action-button primary" onclick="yemenMaps.getDirections(${place.latitude}, ${place.longitude})">
                            <i class="fas fa-directions"></i> اتجاهات
                        </button>
                        <button class="action-button" onclick="yemenMaps.sharePlace(${place.id})">
                            <i class="fas fa-share"></i> مشاركة
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        this.openSidebar();
    }

    async loadNearbyPlaces() {
        const center = this.map.getCenter();
        const zoom = this.map.getZoom();
        
        if (zoom < 10) return; // لا نحمل الأماكن في التكبير المنخفض

        try {
            const response = await fetch(`/api/places/nearby?lat=${center.lat}&lng=${center.lng}&radius=5000&limit=50`);
            const data = await response.json();
            
            if (data.success) {
                this.updateSidebarWithNearbyPlaces(data.places);
            }
        } catch (error) {
            console.error('Error loading nearby places:', error);
        }
    }

    updateSidebarWithNearbyPlaces(places) {
        const sidebarContent = document.getElementById('sidebarContent');
        
        if (places.length === 0) {
            sidebarContent.innerHTML = '<p style="text-align: center; color: #5f6368;">لا توجد أماكن قريبة</p>';
            return;
        }

        const placesHTML = places.map(place => `
            <div class="place-card" onclick="yemenMaps.selectPlace(${place.id})">
                <div class="place-image"></div>
                <div class="place-info">
                    <div class="place-name">${place.name_ar || place.name_en}</div>
                    <div class="place-category">${this.getCategoryName(place.category_id)}</div>
                    ${place.rating ? `
                        <div class="place-rating">
                            <span class="rating-stars">★★★★☆</span>
                            <span class="rating-text">${place.rating}</span>
                        </div>
                    ` : ''}
                </div>
            </div>
        `).join('');

        sidebarContent.innerHTML = placesHTML;
    }

    getCategoryName(categoryId) {
        const categories = {
            1: 'عام',
            2: 'مطعم',
            3: 'مستشفى',
            4: 'مدرسة',
            5: 'مسجد',
            6: 'بنك',
            7: 'محطة وقود',
            8: 'مول تجاري',
            9: 'فندق',
            10: 'صيدلية'
        };
        return categories[categoryId] || 'عام';
    }

    getCurrentLocation() {
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    this.userLocation = [position.coords.latitude, position.coords.longitude];
                    this.addUserLocationMarker();
                },
                (error) => {
                    console.log('Geolocation error:', error);
                }
            );
        }
    }

    addUserLocationMarker() {
        if (this.userLocation) {
            const userIcon = L.divIcon({
                className: 'user-location-marker',
                html: `
                    <div style="
                        width: 20px;
                        height: 20px;
                        background: #4285f4;
                        border: 3px solid #fff;
                        border-radius: 50%;
                        box-shadow: 0 2px 8px rgba(0,0,0,0.3);
                    "></div>
                `,
                iconSize: [20, 20],
                iconAnchor: [10, 10]
            });

            L.marker(this.userLocation, { icon: userIcon })
                .addTo(this.map)
                .bindPopup('موقعك الحالي');
        }
    }

    clearMarkers() {
        this.markers.forEach(marker => {
            this.map.removeLayer(marker);
        });
        this.markers = [];
    }

    openSidebar() {
        document.getElementById('sidebar').classList.add('open');
    }

    closeSidebar() {
        document.getElementById('sidebar').classList.remove('open');
    }

    hideLoading() {
        document.getElementById('loadingSpinner').style.display = 'none';
    }

    showError(message) {
        const spinner = document.getElementById('loadingSpinner');
        spinner.innerHTML = `
            <i class="fas fa-exclamation-triangle" style="color: #ea4335; font-size: 48px;"></i>
            <p style="margin-top: 10px; text-align: center; color: #ea4335;">${message}</p>
        `;
    }

    onMapClick(e) {
        // يمكن إضافة وظائف عند النقر على الخريطة
    }
}

// الدوال العامة
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    sidebar.classList.toggle('open');
}

function changeMapType(type) {
    if (window.yemenMaps) {
        window.yemenMaps.changeMapType(type);
    }
}

function zoomIn() {
    if (window.yemenMaps) {
        window.yemenMaps.map.zoomIn();
    }
}

function zoomOut() {
    if (window.yemenMaps) {
        window.yemenMaps.map.zoomOut();
    }
}

function goToMyLocation() {
    if (window.yemenMaps && window.yemenMaps.userLocation) {
        window.yemenMaps.map.setView(window.yemenMaps.userLocation, 16);
    } else {
        window.yemenMaps.getCurrentLocation();
    }
}

function showDirections() {
    alert('سيتم إضافة نظام الاتجاهات قريباً');
}

function showNearby() {
    if (window.yemenMaps) {
        window.yemenMaps.loadNearbyPlaces();
        window.yemenMaps.openSidebar();
    }
}

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.yemenMaps = new GoogleStyleYemenMaps();
});

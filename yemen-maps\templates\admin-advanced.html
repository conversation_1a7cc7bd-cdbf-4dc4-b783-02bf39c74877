<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم يمن GPS</title>
    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- <PERSON><PERSON>wal Font -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/public/css/custom.css">
    <style>
        body {
            padding-top: 70px;
        }
    </style>
</head>
<body>
    <!-- مؤشر التحميل -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="spinner"></div>
    </div>

    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <img src="img/logo.png" alt="شعار يمن GPS" onerror="this.src='img/placeholder.svg'">
                لوحة تحكم يمن GPS
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                    <li class="nav-item">
                        <a class="nav-link active" href="/admin.html">
                            <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="fas fa-globe"></i> الموقع الرئيسي
                        </a>
                    </li>
                </ul>
                <div class="d-flex">
                    <div class="dropdown">
                        <a class="btn btn-dark dropdown-toggle" href="#" role="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <img src="img/user-avatar.png" alt="صورة المستخدم" class="user-avatar" id="userAvatar" onerror="this.src='img/placeholder.svg'">
                            <span id="userName">المستخدم</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                            <li>
                                <a class="dropdown-item" href="#" id="profileLink">
                                    <i class="fas fa-user"></i> الملف الشخصي
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="#" id="settingsLink">
                                    <i class="fas fa-cog"></i> الإعدادات
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="#" id="logoutBtn">
                                    <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h2 class="card-title">
                            <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                        </h2>
                        <p class="text-muted">مرحباً بك في لوحة تحكم يمن GPS. يمكنك إدارة المستخدمين والمواقع والتصنيفات والعملاء من هنا.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- لوحة الإحصائيات -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 id="usersCount">0</h4>
                                <p class="mb-0">المستخدمين</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-users fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 id="categoriesCount">0</h4>
                                <p class="mb-0">التصنيفات</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-tags fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 id="locationsCount">0</h4>
                                <p class="mb-0">المواقع</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-map-marker-alt fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 id="clientsCount">0</h4>
                                <p class="mb-0">العملاء</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-building fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- علامات التبويب -->
        <ul class="nav nav-tabs mb-4" id="dataTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="users-tab" data-bs-toggle="tab" data-bs-target="#users" type="button" role="tab" aria-controls="users" aria-selected="true">
                    <i class="fas fa-users"></i> المستخدمين
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="locations-tab" data-bs-toggle="tab" data-bs-target="#locations" type="button" role="tab" aria-controls="locations" aria-selected="false">
                    <i class="fas fa-map-marker-alt"></i> المواقع
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="categories-tab" data-bs-toggle="tab" data-bs-target="#categories" type="button" role="tab" aria-controls="categories" aria-selected="false">
                    <i class="fas fa-tags"></i> التصنيفات
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="clients-tab" data-bs-toggle="tab" data-bs-target="#clients" type="button" role="tab" aria-controls="clients" aria-selected="false">
                    <i class="fas fa-building"></i> العملاء
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="maps-tab" data-bs-toggle="tab" data-bs-target="#maps" type="button" role="tab" aria-controls="maps" aria-selected="false">
                    <i class="fas fa-map"></i> الخرائط
                </button>
            </li>
        </ul>

        <!-- محتوى التبويبات -->
        <div class="tab-content" id="dataTabsContent">
            <!-- تبويب المستخدمين -->
            <div class="tab-pane fade show active" id="users" role="tabpanel" aria-labelledby="users-tab">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span>قائمة المستخدمين</span>
                        <div class="d-flex">
                            <button class="btn btn-sm btn-success btn-add" id="addUserBtn">
                                <i class="fas fa-plus"></i> إضافة مستخدم
                            </button>
                            <input type="text" class="form-control form-control-sm" id="usersSearch" placeholder="بحث...">
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>الاسم الكامل</th>
                                        <th>اسم المستخدم</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>رقم الهاتف</th>
                                        <th>الحالة</th>
                                        <th>تاريخ التسجيل</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="usersTable">
                                    <!-- سيتم ملء هذا الجدول بواسطة JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تبويب المواقع -->
            <div class="tab-pane fade" id="locations" role="tabpanel" aria-labelledby="locations-tab">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span>قائمة المواقع</span>
                        <div class="d-flex">
                            <button class="btn btn-sm btn-success btn-add" id="addLocationBtn">
                                <i class="fas fa-plus"></i> إضافة موقع
                            </button>
                            <input type="text" class="form-control form-control-sm" id="locationsSearch" placeholder="بحث...">
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>الاسم</th>
                                        <th>الوصف</th>
                                        <th>التصنيف</th>
                                        <th>الإحداثيات</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="locationsTable">
                                    <!-- سيتم ملء هذا الجدول بواسطة JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تبويب التصنيفات -->
            <div class="tab-pane fade" id="categories" role="tabpanel" aria-labelledby="categories-tab">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span>قائمة التصنيفات</span>
                        <div class="d-flex">
                            <button class="btn btn-sm btn-success btn-add" id="addCategoryBtn">
                                <i class="fas fa-plus"></i> إضافة تصنيف
                            </button>
                            <input type="text" class="form-control form-control-sm" id="categoriesSearch" placeholder="بحث...">
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>التصنيف</th>
                                        <th>الأيقونة</th>
                                        <th>اللون</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="categoriesTable">
                                    <!-- سيتم ملء هذا الجدول بواسطة JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تبويب العملاء -->
            <div class="tab-pane fade" id="clients" role="tabpanel" aria-labelledby="clients-tab">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span>قائمة العملاء</span>
                        <div class="d-flex">
                            <button class="btn btn-sm btn-success btn-add" id="addClientBtn">
                                <i class="fas fa-plus"></i> إضافة عميل
                            </button>
                            <input type="text" class="form-control form-control-sm" id="clientsSearch" placeholder="بحث...">
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>الاسم</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>الهاتف</th>
                                        <th>رقم الجهاز</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="clientsTable">
                                    <!-- سيتم ملء هذا الجدول بواسطة JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تبويب الخرائط -->
            <div class="tab-pane fade" id="maps" role="tabpanel" aria-labelledby="maps-tab">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">إدارة الخرائط</h5>
                        <div>
                            <button class="btn btn-primary btn-sm" onclick="refreshMap()">
                                <i class="fas fa-sync"></i> تحديث الخريطة
                            </button>
                            <button class="btn btn-success btn-sm" onclick="addNewLocation()">
                                <i class="fas fa-plus"></i> إضافة موقع جديد
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- عنصر الخريطة -->
                        <div id="map" style="width: 100%; height: 500px; margin-bottom: 20px;"></div>

                        <!-- أدوات التحكم بالخريطة -->
                        <div class="row mt-3">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>البحث عن موقع:</label>
                                    <input type="text" id="locationSearch" class="form-control" placeholder="أدخل اسم الموقع...">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>تصفية حسب التصنيف:</label>
                                    <select id="categoryFilter" class="form-select">
                                        <option value="">جميع التصنيفات</option>
                                        <!-- سيتم ملء هذه القائمة ديناميكياً -->
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>تصفية حسب العميل:</label>
                                    <select id="clientFilter" class="form-select">
                                        <option value="">جميع العملاء</option>
                                        <!-- سيتم ملء هذه القائمة ديناميكياً -->
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج إضافة/تعديل مستخدم -->
    <div class="modal fade" id="userModal" tabindex="-1" aria-labelledby="userModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="userModalLabel">إضافة مستخدم جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="userForm">
                        <input type="hidden" id="userId">
                        <div class="mb-3">
                            <label for="fullName" class="form-label">الاسم الكامل</label>
                            <input type="text" class="form-control" id="fullName" required>
                        </div>
                        <div class="mb-3">
                            <label for="userUsername" class="form-label">اسم المستخدم</label>
                            <input type="text" class="form-control" id="userUsername" required>
                        </div>
                        <div class="mb-3">
                            <label for="userEmail" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="userEmail" required>
                        </div>
                        <div class="mb-3">
                            <label for="userPhone" class="form-label">رقم الهاتف</label>
                            <input type="text" class="form-control" id="userPhone">
                        </div>
                        <div class="mb-3">
                            <label for="userPassword" class="form-label">كلمة المرور</label>
                            <input type="password" class="form-control" id="userPassword">
                            <small class="form-text text-muted">اتركه فارغاً إذا كنت لا ترغب في تغيير كلمة المرور</small>
                        </div>
                        <div class="mb-3">
                            <label for="userRole" class="form-label">الدور</label>
                            <select class="form-select" id="userRole" required>
                                <option value="1">مدير</option>
                                <option value="2">مستخدم عادي</option>
                                <option value="3">مطور</option>
                            </select>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="userActive" checked>
                            <label class="form-check-label" for="userActive">نشط</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="saveUserBtn">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج إضافة/تعديل موقع -->
    <div class="modal fade" id="locationModal" tabindex="-1" aria-labelledby="locationModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="locationModalLabel">إضافة موقع جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="locationForm">
                        <input type="hidden" id="locationId">
                        <div class="mb-3">
                            <label for="locationName" class="form-label">اسم الموقع</label>
                            <input type="text" class="form-control" id="locationName" required>
                        </div>
                        <div class="mb-3">
                            <label for="locationDescription" class="form-label">الوصف</label>
                            <textarea class="form-control" id="locationDescription" rows="3"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="locationCategory" class="form-label">التصنيف</label>
                            <select class="form-select" id="locationCategory" required>
                                <!-- سيتم ملء هذه القائمة بواسطة JavaScript -->
                            </select>
                        </div>
                        <div class="row mb-3">
                            <div class="col">
                                <label for="locationLat" class="form-label">خط العرض</label>
                                <input type="number" step="any" class="form-control" id="locationLat" required>
                            </div>
                            <div class="col">
                                <label for="locationLng" class="form-label">خط الطول</label>
                                <input type="number" step="any" class="form-control" id="locationLng" required>
                            </div>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="locationActive" checked>
                            <label class="form-check-label" for="locationActive">نشط</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="saveLocationBtn">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج إضافة/تعديل تصنيف -->
    <div class="modal fade" id="categoryModal" tabindex="-1" aria-labelledby="categoryModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="categoryModalLabel">إضافة تصنيف جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="categoryForm">
                        <input type="hidden" id="categoryId">
                        <div class="mb-3">
                            <label for="categoryName" class="form-label">اسم التصنيف</label>
                            <input type="text" class="form-control" id="categoryName" required>
                        </div>
                        <div class="mb-3">
                            <label for="categoryDescription" class="form-label">الوصف</label>
                            <textarea class="form-control" id="categoryDescription" rows="3"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="categoryIcon" class="form-label">الأيقونة</label>
                            <div class="input-group">
                                <span class="input-group-text"><i id="iconPreview" class="fas fa-tag"></i></span>
                                <input type="text" class="form-control" id="categoryIcon" placeholder="fa-tag">
                            </div>
                            <small class="form-text text-muted">أدخل اسم أيقونة Font Awesome (مثال: fa-map-marker)</small>
                        </div>
                        <div class="mb-3">
                            <label for="categoryColor" class="form-label">اللون</label>
                            <input type="color" class="form-control form-control-color" id="categoryColor" value="#198754">
                        </div>
                        <div class="mb-3">
                            <label for="categoryParent" class="form-label">التصنيف الأب</label>
                            <select class="form-select" id="categoryParent">
                                <option value="">بدون تصنيف أب</option>
                                <!-- سيتم ملء هذه القائمة بواسطة JavaScript -->
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="saveCategoryBtn">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج إضافة/تعديل عميل -->
    <div class="modal fade" id="clientModal" tabindex="-1" aria-labelledby="clientModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="clientModalLabel">إضافة عميل جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="clientForm">
                        <input type="hidden" id="clientId">
                        <div class="mb-3">
                            <label for="clientName" class="form-label">اسم العميل</label>
                            <input type="text" class="form-control" id="clientName" required>
                        </div>
                        <div class="mb-3">
                            <label for="clientEmail" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="clientEmail">
                        </div>
                        <div class="mb-3">
                            <label for="clientPhone" class="form-label">رقم الهاتف</label>
                            <input type="text" class="form-control" id="clientPhone">
                        </div>
                        <div class="mb-3">
                            <label for="clientAddress" class="form-label">العنوان</label>
                            <textarea class="form-control" id="clientAddress" rows="2"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="clientDeviceSN" class="form-label">رقم الجهاز</label>
                            <input type="text" class="form-control" id="clientDeviceSN">
                        </div>
                        <div class="mb-3">
                            <label for="clientLicenseN" class="form-label">رقم الترخيص</label>
                            <input type="text" class="form-control" id="clientLicenseN">
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="clientActive" checked>
                            <label class="form-check-label" for="clientActive">نشط</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="saveClientBtn">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج تأكيد الحذف -->
    <div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="deleteConfirmModalLabel">تأكيد الحذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p id="deleteConfirmMessage">هل أنت متأكد من حذف هذا العنصر؟</p>
                    <input type="hidden" id="deleteItemId">
                    <input type="hidden" id="deleteItemType">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">حذف</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- كود لوحة التحكم المستقل -->
    <script>
        // التحقق من المصادقة عند تحميل الصفحة
        function checkAuthentication() {
            // البحث عن بيانات المستخدم في التخزين المحلي أو الجلسة
            let user = null;
            let token = null;

            // التحقق من التخزين المحلي أولاً
            const localUser = localStorage.getItem('adminUser');
            const localToken = localStorage.getItem('yemenGpsToken');

            // ثم التحقق من تخزين الجلسة
            const sessionUser = sessionStorage.getItem('adminUser');
            const sessionToken = sessionStorage.getItem('yemenGpsToken');

            if (localUser) {
                try {
                    user = JSON.parse(localUser);
                    token = localToken;
                } catch (e) {
                    console.error('خطأ في تحليل بيانات المستخدم من التخزين المحلي:', e);
                }
            } else if (sessionUser) {
                try {
                    user = JSON.parse(sessionUser);
                    token = sessionToken;
                } catch (e) {
                    console.error('خطأ في تحليل بيانات المستخدم من تخزين الجلسة:', e);
                }
            }

            // إذا لم يتم العثور على بيانات المستخدم، إعادة التوجيه إلى صفحة تسجيل الدخول
            if (!user) {
                console.log('لم يتم العثور على بيانات المستخدم، إعادة التوجيه إلى صفحة تسجيل الدخول');
                window.location.href = 'admin-login.html';
                return null;
            }

            console.log('تم العثور على بيانات المستخدم:', user);
            return user;
        }

        // تشغيل فحص المصادقة
        const currentUser = checkAuthentication();

        if (currentUser) {
            // تحديث اسم المستخدم في الواجهة
            const userNameElement = document.getElementById('userName');
            if (userNameElement) {
                userNameElement.textContent = currentUser.full_name || currentUser.fullName || currentUser.username || 'مدير النظام';
                console.log('تم تحديث اسم المستخدم في الواجهة:', userNameElement.textContent);
            }

            // تحميل البيانات من قاعدة البيانات
            loadDashboardData();
        }

        // دالة تحميل بيانات لوحة التحكم
        function loadDashboardData() {
            console.log('بدء تحميل بيانات لوحة التحكم...');

            // تحميل المستخدمين
            loadUsers();

            // تحميل التصنيفات
            loadCategories();

            // تحميل المواقع
            loadLocations();

            // تحميل العملاء
            loadClients();
        }

        // دالة تحميل المستخدمين
        function loadUsers() {
            console.log('جاري تحميل المستخدمين...');

            fetch('/api/users')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('فشل في تحميل المستخدمين');
                    }
                    return response.json();
                })
                .then(users => {
                    console.log('تم تحميل المستخدمين:', users);
                    displayUsers(users);
                    updateUsersCount(users.length);
                })
                .catch(error => {
                    console.error('خطأ في تحميل المستخدمين:', error);
                });
        }

        // دالة تحميل التصنيفات
        function loadCategories() {
            console.log('جاري تحميل التصنيفات...');

            fetch('/api/categories')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('فشل في تحميل التصنيفات');
                    }
                    return response.json();
                })
                .then(categories => {
                    console.log('تم تحميل التصنيفات:', categories);
                    displayCategories(categories);
                    updateCategoriesCount(categories.length);
                })
                .catch(error => {
                    console.error('خطأ في تحميل التصنيفات:', error);
                });
        }

        // دالة تحميل المواقع
        function loadLocations() {
            console.log('جاري تحميل المواقع...');

            fetch('/api/admin/locations')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('فشل في تحميل المواقع');
                    }
                    return response.json();
                })
                .then(locations => {
                    console.log('تم تحميل المواقع:', locations);
                    displayLocations(locations);
                    updateLocationsCount(locations.length);
                })
                .catch(error => {
                    console.error('خطأ في تحميل المواقع:', error);
                });
        }

        // دالة تحميل العملاء
        function loadClients() {
            console.log('جاري تحميل العملاء...');

            fetch('/api/clients')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('فشل في تحميل العملاء');
                    }
                    return response.json();
                })
                .then(clients => {
                    console.log('تم تحميل العملاء:', clients);
                    displayClients(clients);
                    updateClientsCount(clients.length);
                })
                .catch(error => {
                    console.error('خطأ في تحميل العملاء:', error);
                });
        }

        // دالة عرض المستخدمين
        function displayUsers(users) {
            console.log('عرض المستخدمين:', users.length, 'مستخدم');
            const tbody = document.querySelector('#usersTable');
            if (!tbody) {
                console.error('لم يتم العثور على جدول المستخدمين');
                return;
            }

            tbody.innerHTML = '';

            users.forEach(user => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${user.user_id}</td>
                    <td>${user.username}</td>
                    <td>${user.full_name || 'غير محدد'}</td>
                    <td>${user.email}</td>
                    <td>${user.phone || 'غير محدد'}</td>
                    <td><span class="badge ${user.is_active ? 'bg-success' : 'bg-danger'}">${user.is_active ? 'نشط' : 'غير نشط'}</span></td>
                    <td>${new Date(user.registration_date).toLocaleDateString('ar-SA')}</td>
                    <td>
                        <button class="btn btn-sm btn-primary me-1" onclick="editUser(${user.user_id})">تعديل</button>
                        <button class="btn btn-sm btn-danger" onclick="deleteUser(${user.user_id})">حذف</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
            console.log('تم عرض المستخدمين في الجدول بنجاح');
        }

        // دالة عرض التصنيفات
        function displayCategories(categories) {
            console.log('عرض التصنيفات:', categories.length, 'تصنيف');
            const tbody = document.querySelector('#categoriesTable');
            if (!tbody) {
                console.error('لم يتم العثور على جدول التصنيفات');
                return;
            }

            tbody.innerHTML = '';

            categories.forEach(category => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${category.id}</td>
                    <td>${category.name}</td>
                    <td><i class="${category.icon}" style="color: ${category.color}"></i> ${category.icon}</td>
                    <td><span class="badge" style="background-color: ${category.color}">${category.color}</span></td>
                    <td>${new Date(category.created_at).toLocaleDateString('ar-SA')}</td>
                    <td>
                        <button class="btn btn-sm btn-primary me-1" onclick="editCategory(${category.id})">تعديل</button>
                        <button class="btn btn-sm btn-danger" onclick="deleteCategory(${category.id})">حذف</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
            console.log('تم عرض التصنيفات في الجدول بنجاح');
        }

        // دالة عرض المواقع
        function displayLocations(locations) {
            console.log('عرض المواقع:', locations.length, 'موقع');
            const tbody = document.querySelector('#locationsTable');
            if (!tbody) {
                console.error('لم يتم العثور على جدول المواقع');
                return;
            }

            tbody.innerHTML = '';

            locations.forEach(location => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${location.id}</td>
                    <td>${location.name}</td>
                    <td>${location.description || 'غير محدد'}</td>
                    <td>${location.category_name || 'غير محدد'}</td>
                    <td>${location.lat}, ${location.lng}</td>
                    <td><span class="badge ${location.status === 'active' ? 'bg-success' : 'bg-warning'}">${location.status === 'active' ? 'نشط' : 'غير نشط'}</span></td>
                    <td>
                        <button class="btn btn-sm btn-primary me-1" onclick="editLocation(${location.id})">تعديل</button>
                        <button class="btn btn-sm btn-danger" onclick="deleteLocation(${location.id})">حذف</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
            console.log('تم عرض المواقع في الجدول بنجاح');
        }

        // دالة عرض العملاء
        function displayClients(clients) {
            console.log('عرض العملاء:', clients.length, 'عميل');
            const tbody = document.querySelector('#clientsTable');
            if (!tbody) {
                console.error('لم يتم العثور على جدول العملاء');
                return;
            }

            tbody.innerHTML = '';

            clients.forEach(client => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${client.id}</td>
                    <td>${client.name}</td>
                    <td>${client.email}</td>
                    <td>${client.phone}</td>
                    <td>${client.devicesn || 'غير محدد'}</td>
                    <td><span class="badge ${client.status === 'active' ? 'bg-success' : 'bg-warning'}">${client.status === 'active' ? 'نشط' : 'غير نشط'}</span></td>
                    <td>
                        <button class="btn btn-sm btn-primary me-1" onclick="editClient(${client.id})">تعديل</button>
                        <button class="btn btn-sm btn-danger" onclick="deleteClient(${client.id})">حذف</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
            console.log('تم عرض العملاء في الجدول بنجاح');
        }

        // إعداد زر تسجيل الخروج
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', function() {
                console.log('تم النقر على زر تسجيل الخروج');
                // حذف جميع بيانات المستخدم من التخزين المحلي
                sessionStorage.clear();
                localStorage.clear();

                // التوجيه إلى صفحة تسجيل الدخول
                window.location.href = 'admin-login.html';
            });
            console.log('تم إعداد زر تسجيل الخروج');
        }

        // إعداد أحداث النقر على علامات التبويب
        const tabButtons = document.querySelectorAll('.nav-link');
        tabButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // إزالة الفئة النشطة من جميع الأزرار
                tabButtons.forEach(btn => btn.classList.remove('active'));
                // إضافة الفئة النشطة للزر المنقور
                this.classList.add('active');
            });
        });

        // إعداد أحداث النماذج
        document.querySelectorAll('.btn-add').forEach(button => {
            button.addEventListener('click', function() {
                const targetModal = this.getAttribute('data-bs-target') || '#userModal';
                const modal = new bootstrap.Modal(document.querySelector(targetModal));
                modal.show();
            });
        });

        // منع إعادة التوجيه عند إعادة تحميل الصفحة
        window.onbeforeunload = function() {
            // تخزين حالة التبويب الحالية
            const activeTab = document.querySelector('.nav-link.active');
            if (activeTab) {
                sessionStorage.setItem('activeTab', activeTab.id);
            }
        };

        // استعادة حالة التبويب عند إعادة تحميل الصفحة
        const savedTab = sessionStorage.getItem('activeTab');
        if (savedTab) {
            const tabToActivate = document.getElementById(savedTab);
            if (tabToActivate) {
                tabToActivate.click();
            }
        }

        // وظائف التعديل والحذف للمستخدمين
        function editUser(userId) {
            console.log('تعديل المستخدم:', userId);
            // TODO: تنفيذ وظيفة التعديل
            alert('سيتم تنفيذ وظيفة تعديل المستخدم قريباً');
        }

        function deleteUser(userId) {
            console.log('حذف المستخدم:', userId);
            if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
                // TODO: تنفيذ وظيفة الحذف
                alert('سيتم تنفيذ وظيفة حذف المستخدم قريباً');
            }
        }

        // وظائف التعديل والحذف للتصنيفات
        function editCategory(categoryId) {
            console.log('تعديل التصنيف:', categoryId);
            alert('سيتم تنفيذ وظيفة تعديل التصنيف قريباً');
        }

        function deleteCategory(categoryId) {
            console.log('حذف التصنيف:', categoryId);
            if (confirm('هل أنت متأكد من حذف هذا التصنيف؟')) {
                alert('سيتم تنفيذ وظيفة حذف التصنيف قريباً');
            }
        }

        // وظائف التعديل والحذف للمواقع
        function editLocation(locationId) {
            console.log('تعديل الموقع:', locationId);
            alert('سيتم تنفيذ وظيفة تعديل الموقع قريباً');
        }

        function deleteLocation(locationId) {
            console.log('حذف الموقع:', locationId);
            if (confirm('هل أنت متأكد من حذف هذا الموقع؟')) {
                alert('سيتم تنفيذ وظيفة حذف الموقع قريباً');
            }
        }

        // وظائف التعديل والحذف للعملاء
        function editClient(clientId) {
            console.log('تعديل العميل:', clientId);
            alert('سيتم تنفيذ وظيفة تعديل العميل قريباً');
        }

        function deleteClient(clientId) {
            console.log('حذف العميل:', clientId);
            if (confirm('هل أنت متأكد من حذف هذا العميل؟')) {
                alert('سيتم تنفيذ وظيفة حذف العميل قريباً');
            }
        }

        // وظيفة البحث في الجداول
        function setupSearch() {
            // البحث في المستخدمين
            const usersSearch = document.getElementById('usersSearch');
            if (usersSearch) {
                usersSearch.addEventListener('input', function() {
                    filterTable('usersTable', this.value);
                });
            }

            // البحث في التصنيفات
            const categoriesSearch = document.getElementById('categoriesSearch');
            if (categoriesSearch) {
                categoriesSearch.addEventListener('input', function() {
                    filterTable('categoriesTable', this.value);
                });
            }

            // البحث في المواقع
            const locationsSearch = document.getElementById('locationsSearch');
            if (locationsSearch) {
                locationsSearch.addEventListener('input', function() {
                    filterTable('locationsTable', this.value);
                });
            }

            // البحث في العملاء
            const clientsSearch = document.getElementById('clientsSearch');
            if (clientsSearch) {
                clientsSearch.addEventListener('input', function() {
                    filterTable('clientsTable', this.value);
                });
            }
        }

        // دالة تصفية الجدول
        function filterTable(tableId, searchTerm) {
            const table = document.getElementById(tableId);
            if (!table) return;

            const rows = table.getElementsByTagName('tr');
            searchTerm = searchTerm.toLowerCase();

            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];
                const cells = row.getElementsByTagName('td');
                let found = false;

                for (let j = 0; j < cells.length; j++) {
                    const cellText = cells[j].textContent || cells[j].innerText;
                    if (cellText.toLowerCase().indexOf(searchTerm) > -1) {
                        found = true;
                        break;
                    }
                }

                if (found) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            }
        }

        // تشغيل وظيفة البحث عند تحميل الصفحة
        setupSearch();

        // دوال تحديث الإحصائيات
        function updateUsersCount(count) {
            const element = document.getElementById('usersCount');
            if (element) {
                element.textContent = count;
                console.log('تم تحديث عدد المستخدمين:', count);
            }
        }

        function updateCategoriesCount(count) {
            const element = document.getElementById('categoriesCount');
            if (element) {
                element.textContent = count;
                console.log('تم تحديث عدد التصنيفات:', count);
            }
        }

        function updateLocationsCount(count) {
            const element = document.getElementById('locationsCount');
            if (element) {
                element.textContent = count;
                console.log('تم تحديث عدد المواقع:', count);
            }
        }

        function updateClientsCount(count) {
            const element = document.getElementById('clientsCount');
            if (element) {
                element.textContent = count;
                console.log('تم تحديث عدد العملاء:', count);
            }
        }

        // كود الخرائط - Google Maps
        let map;
        let markers = [];

        // دالة تهيئة الخريطة
        function initMap() {
            console.log('جاري تهيئة الخريطة...');

            // إحداثيات اليمن الافتراضية (صنعاء)
            const yemen = { lat: 15.3694, lng: 44.1910 };

            // إنشاء خريطة جديدة
            map = new google.maps.Map(document.getElementById("map"), {
                center: yemen,
                zoom: 8,
                mapTypeId: "roadmap",
                mapTypeControl: true,
                streetViewControl: true,
            });

            // إضافة أحداث الخريطة
            setupMapEvents();

            // تحميل المواقع من قاعدة البيانات
            loadLocationsOnMap();

            console.log('تم تهيئة الخريطة بنجاح');
        }

        // إعداد أحداث الخريطة
        function setupMapEvents() {
            // إضافة حدث النقر على الخريطة لإضافة موقع جديد
            map.addListener('click', function(event) {
                const clickedLocation = {
                    lat: event.latLng.lat(),
                    lng: event.latLng.lng()
                };

                if (confirm('هل تريد إضافة موقع جديد في هذه النقطة؟')) {
                    showAddLocationForm(clickedLocation);
                }
            });
        }

        // عرض نموذج إضافة موقع جديد
        function showAddLocationForm(location) {
            // إنشاء نافذة معلومات مع نموذج
            const infoWindow = new google.maps.InfoWindow({
                content: `
                    <div style="width: 300px;">
                        <h5>إضافة موقع جديد</h5>
                        <form id="addLocationForm">
                            <div class="mb-2">
                                <label class="form-label">الاسم</label>
                                <input type="text" class="form-control" id="locationName" required>
                            </div>
                            <div class="mb-2">
                                <label class="form-label">الوصف</label>
                                <textarea class="form-control" id="locationDescription"></textarea>
                            </div>
                            <div class="mb-2">
                                <label class="form-label">التصنيف</label>
                                <select class="form-select" id="locationCategory" required>
                                    <option value="">اختر التصنيف...</option>
                                    <!-- سيتم ملء هذه القائمة ديناميكياً -->
                                </select>
                            </div>
                            <div class="mb-2">
                                <label class="form-label">العنوان</label>
                                <input type="text" class="form-control" id="locationAddress">
                            </div>
                            <div class="mb-2">
                                <label class="form-label">الهاتف</label>
                                <input type="text" class="form-control" id="locationPhone">
                            </div>
                            <input type="hidden" id="locationLat" value="${location.lat}">
                            <input type="hidden" id="locationLng" value="${location.lng}">
                            <button type="button" class="btn btn-primary" onclick="saveNewLocation()">حفظ</button>
                        </form>
                    </div>
                `
            });

            // إنشاء علامة مؤقتة
            const marker = new google.maps.Marker({
                position: location,
                map: map,
                animation: google.maps.Animation.DROP
            });

            // فتح نافذة المعلومات
            infoWindow.open(map, marker);

            // تحميل التصنيفات في القائمة المنسدلة
            loadCategoriesForSelect();
        }

        // تحميل التصنيفات للقائمة المنسدلة
        function loadCategoriesForSelect() {
            fetch('/api/categories')
                .then(response => response.json())
                .then(categories => {
                    const select = document.getElementById('locationCategory');
                    if (!select) return;

                    categories.forEach(category => {
                        const option = document.createElement('option');
                        option.value = category.id;
                        option.textContent = category.name;
                        select.appendChild(option);
                    });
                })
                .catch(error => console.error('خطأ في تحميل التصنيفات:', error));
        }

        // حفظ موقع جديد
        function saveNewLocation() {
            const name = document.getElementById('locationName').value;
            const description = document.getElementById('locationDescription').value;
            const categoryId = document.getElementById('locationCategory').value;
            const address = document.getElementById('locationAddress').value;
            const phone = document.getElementById('locationPhone').value;
            const lat = document.getElementById('locationLat').value;
            const lng = document.getElementById('locationLng').value;

            if (!name || !categoryId) {
                alert('يرجى ملء الحقول المطلوبة');
                return;
            }

            const locationData = {
                nameAr: name,
                nameEn: name,
                lat: parseFloat(lat),
                lng: parseFloat(lng),
                address: address,
                phone: phone,
                categoryId: parseInt(categoryId),
                clientId: 1, // العميل الافتراضي
                description: description
            };

            fetch('/api/admin/locations', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(locationData)
            })
            .then(response => response.json())
            .then(data => {
                alert('تم إضافة الموقع بنجاح!');
                loadLocationsOnMap(); // تحديث الخريطة
            })
            .catch(error => console.error('خطأ في حفظ الموقع:', error));
        }

        // تحميل المواقع على الخريطة
        function loadLocationsOnMap() {
            // مسح العلامات الحالية
            clearMarkers();

            fetch('/api/admin/locations')
                .then(response => response.json())
                .then(locations => {
                    console.log('تم تحميل المواقع:', locations.length, 'موقع');

                    // إنشاء علامات لكل موقع
                    locations.forEach(location => {
                        addMarkerToMap(location);
                    });
                })
                .catch(error => console.error('خطأ في تحميل المواقع على الخريطة:', error));
        }

        // إضافة علامة إلى الخريطة
        function addMarkerToMap(location) {
            const marker = new google.maps.Marker({
                position: {
                    lat: parseFloat(location.lat),
                    lng: parseFloat(location.lng)
                },
                map: map,
                title: location.name_ar || location.name,
                icon: getCategoryIcon(location.category_id)
            });

            // إضافة نافذة معلومات
            const infoWindow = new google.maps.InfoWindow({
                content: `
                    <div style="width: 250px;">
                        <h5>${location.name_ar || location.name}</h5>
                        <p>${location.description || ''}</p>
                        <p><strong>العنوان:</strong> ${location.address || 'غير متوفر'}</p>
                        <p><strong>الهاتف:</strong> ${location.phone || 'غير متوفر'}</p>
                        <p><strong>التصنيف:</strong> ${location.category_name || 'غير محدد'}</p>
                        <div class="mt-2">
                            <button class="btn btn-sm btn-primary" onclick="editLocation(${location.id})">تعديل</button>
                            <button class="btn btn-sm btn-danger" onclick="deleteLocation(${location.id})">حذف</button>
                        </div>
                    </div>
                `
            });

            marker.addListener('click', () => {
                infoWindow.open(map, marker);
            });

            // إضافة العلامة إلى المصفوفة
            markers.push(marker);
        }

        // الحصول على أيقونة التصنيف
        function getCategoryIcon(categoryId) {
            // يمكنك تخصيص الأيقونات حسب التصنيفات
            const icons = {
                1: 'http://maps.google.com/mapfiles/ms/icons/red-dot.png',
                2: 'http://maps.google.com/mapfiles/ms/icons/blue-dot.png',
                3: 'http://maps.google.com/mapfiles/ms/icons/green-dot.png',
                4: 'http://maps.google.com/mapfiles/ms/icons/yellow-dot.png',
                5: 'http://maps.google.com/mapfiles/ms/icons/purple-dot.png'
            };

            return icons[categoryId] || 'http://maps.google.com/mapfiles/ms/icons/red-dot.png';
        }

        // مسح جميع العلامات
        function clearMarkers() {
            markers.forEach(marker => marker.setMap(null));
            markers = [];
        }

        // تحديث الخريطة
        function refreshMap() {
            loadLocationsOnMap();
        }

        // إضافة موقع جديد
        function addNewLocation() {
            alert('انقر على الخريطة لتحديد موقع جديد');
        }
    </script>

    <!-- Google Maps JavaScript API -->
    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyD8S6CJpsAHMR5dG-J_ecX2By_87y-sAv0&libraries=places,visualization,drawing&v=weekly&callback=initMap" defer></script>
</body>
</html>

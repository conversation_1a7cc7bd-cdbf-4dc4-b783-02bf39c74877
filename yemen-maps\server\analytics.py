#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Yemen Maps - Analytics System
نظام الإحصائيات والتحليلات
"""

import psycopg2
import psycopg2.extras
from datetime import datetime, timedelta
import json

class YemenMapsAnalytics:
    def __init__(self, db_config):
        self.db_config = db_config
        self.conn = None
    
    def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            self.conn = psycopg2.connect(**self.db_config)
            return True
        except Exception as e:
            print(f"Database connection error: {e}")
            return False
    
    def get_general_stats(self):
        """إحصائيات عامة"""
        if not self.connect():
            return {}
        
        try:
            cursor = self.conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            
            # إجمالي الأماكن
            cursor.execute("SELECT COUNT(*) as total FROM places WHERE is_active = true")
            total_places = cursor.fetchone()['total']
            
            # إجمالي الصور
            cursor.execute("SELECT COUNT(*) as total FROM place_photos")
            total_photos = cursor.fetchone()['total']
            
            # إجمالي المحافظات
            cursor.execute("SELECT COUNT(DISTINCT governorate_id) as total FROM places WHERE is_active = true")
            total_governorates = cursor.fetchone()['total']
            
            # إجمالي الفئات
            cursor.execute("SELECT COUNT(DISTINCT category_id) as total FROM places WHERE is_active = true")
            total_categories = cursor.fetchone()['total']
            
            # متوسط التقييم
            cursor.execute("SELECT AVG(rating) as avg_rating FROM places WHERE rating IS NOT NULL")
            avg_rating = cursor.fetchone()['avg_rating']
            
            return {
                'total_places': total_places,
                'total_photos': total_photos,
                'total_governorates': total_governorates,
                'total_categories': total_categories,
                'avg_rating': round(float(avg_rating or 0), 2)
            }
            
        except Exception as e:
            print(f"Error getting general stats: {e}")
            return {}
        finally:
            if self.conn:
                self.conn.close()
    
    def get_places_by_governorate(self):
        """إحصائيات الأماكن حسب المحافظة"""
        if not self.connect():
            return []
        
        try:
            cursor = self.conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            
            cursor.execute("""
                SELECT 
                    g.name_ar as governorate_name,
                    COUNT(p.id) as places_count,
                    COUNT(pp.id) as photos_count
                FROM governorates g
                LEFT JOIN places p ON g.id = p.governorate_id AND p.is_active = true
                LEFT JOIN place_photos pp ON p.id = pp.place_id
                GROUP BY g.id, g.name_ar
                ORDER BY places_count DESC
            """)
            
            return cursor.fetchall()
            
        except Exception as e:
            print(f"Error getting governorate stats: {e}")
            return []
        finally:
            if self.conn:
                self.conn.close()
    
    def get_places_by_category(self):
        """إحصائيات الأماكن حسب الفئة"""
        if not self.connect():
            return []
        
        try:
            cursor = self.conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            
            cursor.execute("""
                SELECT 
                    c.name_ar as category_name,
                    COUNT(p.id) as places_count,
                    AVG(p.rating) as avg_rating
                FROM categories c
                LEFT JOIN places p ON c.id = p.category_id AND p.is_active = true
                GROUP BY c.id, c.name_ar
                ORDER BY places_count DESC
            """)
            
            return cursor.fetchall()
            
        except Exception as e:
            print(f"Error getting category stats: {e}")
            return []
        finally:
            if self.conn:
                self.conn.close()
    
    def get_top_rated_places(self, limit=10):
        """أفضل الأماكن تقييماً"""
        if not self.connect():
            return []
        
        try:
            cursor = self.conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            
            cursor.execute("""
                SELECT 
                    p.name_ar,
                    p.name_en,
                    p.rating,
                    g.name_ar as governorate_name,
                    c.name_ar as category_name
                FROM places p
                LEFT JOIN governorates g ON p.governorate_id = g.id
                LEFT JOIN categories c ON p.category_id = c.id
                WHERE p.is_active = true AND p.rating IS NOT NULL
                ORDER BY p.rating DESC, p.id
                LIMIT %s
            """, [limit])
            
            return cursor.fetchall()
            
        except Exception as e:
            print(f"Error getting top rated places: {e}")
            return []
        finally:
            if self.conn:
                self.conn.close()
    
    def generate_dashboard_data(self):
        """إنشاء بيانات لوحة المعلومات"""
        return {
            'general_stats': self.get_general_stats(),
            'governorate_stats': self.get_places_by_governorate(),
            'category_stats': self.get_places_by_category(),
            'top_rated_places': self.get_top_rated_places(),
            'generated_at': datetime.now().isoformat()
        }

def create_analytics_api(app, db_config):
    """إضافة نقاط نهاية الإحصائيات للتطبيق"""
    analytics = YemenMapsAnalytics(db_config)
    
    @app.route('/api/analytics/general')
    def get_general_analytics():
        """إحصائيات عامة"""
        try:
            stats = analytics.get_general_stats()
            return {
                'success': True,
                'data': stats
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }, 500
    
    @app.route('/api/analytics/governorates')
    def get_governorate_analytics():
        """إحصائيات المحافظات"""
        try:
            stats = analytics.get_places_by_governorate()
            return {
                'success': True,
                'data': stats
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }, 500
    
    @app.route('/api/analytics/categories')
    def get_category_analytics():
        """إحصائيات الفئات"""
        try:
            stats = analytics.get_places_by_category()
            return {
                'success': True,
                'data': stats
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }, 500
    
    @app.route('/api/analytics/dashboard')
    def get_dashboard_analytics():
        """بيانات لوحة المعلومات الشاملة"""
        try:
            dashboard_data = analytics.generate_dashboard_data()
            return {
                'success': True,
                'data': dashboard_data
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }, 500

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Yemen Maps - Quick Fix for Photos
إصلاح سريع لمشكلة الصور
"""

import os
import psycopg2
import psycopg2.extras
from pathlib import Path
from PIL import Image

# إعداد قاعدة البيانات
DB_CONFIG = {
    'host': 'localhost',
    'database': 'yemen_gps',
    'user': 'yemen',
    'password': 'admin',
    'port': 5432
}

def create_simple_photos():
    """إنشاء صور بسيطة وإضافتها للأماكن"""
    
    # الاتصال بقاعدة البيانات
    conn = psycopg2.connect(**DB_CONFIG)
    cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
    
    print("🔧 إنشاء جدول الصور...")
    
    # إنشاء جدول الصور البسيط
    cursor.execute("""
        DROP TABLE IF EXISTS place_photos;
        CREATE TABLE place_photos (
            id SERIAL PRIMARY KEY,
            place_id INTEGER REFERENCES places(id),
            photo_path VARCHAR(500) NOT NULL,
            is_primary BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
    """)
    
    print("📸 إنشاء الصور النموذجية...")
    
    # إنشاء مجلد الصور
    images_dir = Path("images/places")
    images_dir.mkdir(parents=True, exist_ok=True)
    
    # ألوان مختلفة للفئات
    colors = [
        (255, 107, 107),  # أحمر
        (78, 205, 196),   # أخضر مائي
        (69, 183, 209),   # أزرق
        (150, 206, 180),  # أخضر فاتح
        (255, 234, 167),  # أصفر
        (221, 160, 221),  # بنفسجي
        (152, 216, 200),  # أخضر نعناعي
        (247, 220, 111),  # ذهبي
        (187, 143, 206),  # بنفسجي فاتح
        (149, 165, 166)   # رمادي
    ]
    
    # إنشاء صور نموذجية
    for i, color in enumerate(colors):
        img = Image.new('RGB', (400, 300), color)
        img_path = images_dir / f"sample_{i+1}.jpg"
        img.save(img_path, 'JPEG', quality=85)
        print(f"✅ تم إنشاء الصورة: {img_path}")
    
    print("💾 إضافة الصور لقاعدة البيانات...")
    
    # جلب الأماكن (أول 1000 مكان)
    cursor.execute("""
        SELECT id FROM places 
        WHERE is_active = true 
        ORDER BY id 
        LIMIT 1000
    """)
    
    places = cursor.fetchall()
    print(f"📊 تم العثور على {len(places)} مكان")
    
    # إضافة صورة لكل مكان
    for i, place in enumerate(places):
        color_index = (i % len(colors)) + 1
        photo_path = f"places/sample_{color_index}.jpg"
        
        cursor.execute("""
            INSERT INTO place_photos (place_id, photo_path, is_primary)
            VALUES (%s, %s, %s)
        """, [place['id'], photo_path, True])
        
        if (i + 1) % 100 == 0:
            print(f"✅ تم إضافة {i + 1} صورة")
    
    # حفظ التغييرات
    conn.commit()
    
    # التحقق من النتائج
    cursor.execute("SELECT COUNT(*) as count FROM place_photos")
    photos_count = cursor.fetchone()['count']
    
    print("=" * 50)
    print("🎉 تم الإصلاح بنجاح!")
    print(f"📸 إجمالي الصور: {photos_count}")
    print("=" * 50)
    
    conn.close()

if __name__ == "__main__":
    create_simple_photos()

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إحصائيات خرائط اليمن</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .dashboard-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px;
            padding: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .stat-label {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .chart-container {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .page-header {
            text-align: center;
            margin-bottom: 40px;
            color: #333;
        }
        
        .page-header h1 {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .page-header p {
            font-size: 1.2rem;
            color: #666;
        }
        
        .loading {
            text-align: center;
            padding: 50px;
        }
        
        .loading i {
            font-size: 3rem;
            color: #667eea;
            animation: spin 2s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .table-container {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .table th {
            background: #667eea;
            color: white;
            border: none;
        }
        
        .navbar-custom {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-map-marked-alt text-primary"></i>
                <strong>خرائط اليمن</strong>
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">الرئيسية</a>
                <a class="nav-link" href="/advanced">الواجهة المتقدمة</a>
                <a class="nav-link" href="/admin">الإدارة</a>
                <a class="nav-link active" href="/analytics">الإحصائيات</a>
            </div>
        </div>
    </nav>

    <div class="dashboard-container">
        <!-- Page Header -->
        <div class="page-header">
            <h1><i class="fas fa-chart-bar"></i> إحصائيات خرائط اليمن</h1>
            <p>تحليلات شاملة لقاعدة بيانات الأماكن اليمنية</p>
        </div>

        <!-- Loading -->
        <div id="loading" class="loading">
            <i class="fas fa-spinner"></i>
            <p>جاري تحميل الإحصائيات...</p>
        </div>

        <!-- Dashboard Content -->
        <div id="dashboard-content" style="display: none;">
            <!-- General Stats -->
            <div class="row" id="general-stats">
                <!-- Stats cards will be inserted here -->
            </div>

            <!-- Charts Row -->
            <div class="row">
                <!-- Governorates Chart -->
                <div class="col-lg-6">
                    <div class="chart-container">
                        <h4><i class="fas fa-map"></i> توزيع الأماكن حسب المحافظة</h4>
                        <canvas id="governoratesChart"></canvas>
                    </div>
                </div>

                <!-- Categories Chart -->
                <div class="col-lg-6">
                    <div class="chart-container">
                        <h4><i class="fas fa-tags"></i> توزيع الأماكن حسب الفئة</h4>
                        <canvas id="categoriesChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Top Rated Places -->
            <div class="table-container">
                <h4><i class="fas fa-star"></i> أفضل الأماكن تقييماً</h4>
                <div class="table-responsive">
                    <table class="table table-hover" id="top-places-table">
                        <thead>
                            <tr>
                                <th>الترتيب</th>
                                <th>اسم المكان</th>
                                <th>المحافظة</th>
                                <th>الفئة</th>
                                <th>التقييم</th>
                            </tr>
                        </thead>
                        <tbody id="top-places-body">
                            <!-- Data will be inserted here -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Governorates Table -->
            <div class="table-container">
                <h4><i class="fas fa-list"></i> إحصائيات المحافظات</h4>
                <div class="table-responsive">
                    <table class="table table-hover" id="governorates-table">
                        <thead>
                            <tr>
                                <th>المحافظة</th>
                                <th>عدد الأماكن</th>
                                <th>عدد الصور</th>
                                <th>النسبة</th>
                            </tr>
                        </thead>
                        <tbody id="governorates-body">
                            <!-- Data will be inserted here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        class YemenMapsAnalytics {
            constructor() {
                this.charts = {};
                this.data = {};
                this.init();
            }

            async init() {
                try {
                    await this.loadData();
                    this.renderGeneralStats();
                    this.renderCharts();
                    this.renderTables();
                    this.hideLoading();
                } catch (error) {
                    console.error('Error loading analytics:', error);
                    this.showError();
                }
            }

            async loadData() {
                const response = await fetch('/api/analytics/dashboard');
                const result = await response.json();
                
                if (result.success) {
                    this.data = result.data;
                } else {
                    throw new Error(result.error || 'Failed to load data');
                }
            }

            renderGeneralStats() {
                const stats = this.data.general_stats;
                const container = document.getElementById('general-stats');
                
                const statsCards = [
                    { icon: 'fas fa-map-marker-alt', label: 'إجمالي الأماكن', value: stats.total_places, color: '#667eea' },
                    { icon: 'fas fa-images', label: 'إجمالي الصور', value: stats.total_photos, color: '#764ba2' },
                    { icon: 'fas fa-map', label: 'المحافظات', value: stats.total_governorates, color: '#f093fb' },
                    { icon: 'fas fa-tags', label: 'الفئات', value: stats.total_categories, color: '#f5576c' },
                    { icon: 'fas fa-star', label: 'متوسط التقييم', value: stats.avg_rating, color: '#4facfe' }
                ];

                container.innerHTML = statsCards.map(stat => `
                    <div class="col-lg-2 col-md-4 col-sm-6">
                        <div class="stat-card" style="background: linear-gradient(135deg, ${stat.color} 0%, ${stat.color}aa 100%);">
                            <div class="text-center">
                                <i class="${stat.icon}" style="font-size: 2rem; margin-bottom: 15px;"></i>
                                <div class="stat-number">${stat.value}</div>
                                <div class="stat-label">${stat.label}</div>
                            </div>
                        </div>
                    </div>
                `).join('');
            }

            renderCharts() {
                this.renderGovernoratesChart();
                this.renderCategoriesChart();
            }

            renderGovernoratesChart() {
                const ctx = document.getElementById('governoratesChart').getContext('2d');
                const data = this.data.governorate_stats.slice(0, 10); // Top 10
                
                this.charts.governorates = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: data.map(item => item.governorate_name),
                        datasets: [{
                            label: 'عدد الأماكن',
                            data: data.map(item => item.places_count),
                            backgroundColor: 'rgba(102, 126, 234, 0.8)',
                            borderColor: 'rgba(102, 126, 234, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }

            renderCategoriesChart() {
                const ctx = document.getElementById('categoriesChart').getContext('2d');
                const data = this.data.category_stats;
                
                this.charts.categories = new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: data.map(item => item.category_name),
                        datasets: [{
                            data: data.map(item => item.places_count),
                            backgroundColor: [
                                '#667eea', '#764ba2', '#f093fb', '#f5576c',
                                '#4facfe', '#00f2fe', '#43e97b', '#38f9d7',
                                '#ffecd2', '#fcb69f'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }

            renderTables() {
                this.renderTopPlacesTable();
                this.renderGovernoratesTable();
            }

            renderTopPlacesTable() {
                const tbody = document.getElementById('top-places-body');
                const places = this.data.top_rated_places;
                
                tbody.innerHTML = places.map((place, index) => `
                    <tr>
                        <td><span class="badge bg-primary">${index + 1}</span></td>
                        <td><strong>${place.name_ar || place.name_en}</strong></td>
                        <td>${place.governorate_name || 'غير محدد'}</td>
                        <td>${place.category_name || 'عام'}</td>
                        <td>
                            <span class="text-warning">
                                ${'★'.repeat(Math.floor(place.rating))} ${place.rating}
                            </span>
                        </td>
                    </tr>
                `).join('');
            }

            renderGovernoratesTable() {
                const tbody = document.getElementById('governorates-body');
                const governorates = this.data.governorate_stats;
                const totalPlaces = this.data.general_stats.total_places;
                
                tbody.innerHTML = governorates.map(gov => {
                    const percentage = ((gov.places_count / totalPlaces) * 100).toFixed(1);
                    return `
                        <tr>
                            <td><strong>${gov.governorate_name}</strong></td>
                            <td><span class="badge bg-info">${gov.places_count}</span></td>
                            <td><span class="badge bg-success">${gov.photos_count}</span></td>
                            <td>
                                <div class="progress" style="height: 20px;">
                                    <div class="progress-bar" style="width: ${percentage}%">${percentage}%</div>
                                </div>
                            </td>
                        </tr>
                    `;
                }).join('');
            }

            hideLoading() {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('dashboard-content').style.display = 'block';
            }

            showError() {
                document.getElementById('loading').innerHTML = `
                    <i class="fas fa-exclamation-triangle text-danger"></i>
                    <p>حدث خطأ في تحميل الإحصائيات</p>
                `;
            }
        }

        // Initialize analytics when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new YemenMapsAnalytics();
        });
    </script>
</body>
</html>

/**
 * Yemen GPS Admin Dashboard Advanced JavaScript
 * ملف JavaScript المتقدم للوحة تحكم يمن GPS
 */

// متغيرات عامة
let map;
let markers = [];
let currentUser = null;
let authToken = null;

// التحقق من تسجيل الدخول
function checkAuth() {
    const localUser = localStorage.getItem('adminUser');
    const sessionUser = sessionStorage.getItem('adminUser');
    const localToken = localStorage.getItem('yemenGpsToken');
    const sessionToken = sessionStorage.getItem('yemenGpsToken');
    
    if (!localUser && !sessionUser) {
        console.log('المستخدم غير مسجل دخول، إعادة التوجيه إلى صفحة تسجيل الدخول');
        window.location.href = '/login';
        return false;
    }
    
    currentUser = JSON.parse(localUser || sessionUser);
    authToken = localToken || sessionToken;
    console.log('المستخدم مسجل دخول:', currentUser);
    
    // تحديث واجهة المستخدم
    if (currentUser) {
        document.getElementById('userName').textContent = currentUser.full_name || currentUser.username || 'المستخدم';
        if (currentUser.avatar) {
            document.getElementById('userAvatar').src = currentUser.avatar;
        }
    }
    
    return true;
}

// إظهار مؤشر التحميل
function showLoading() {
    document.getElementById('loadingOverlay').style.display = 'flex';
}

// إخفاء مؤشر التحميل
function hideLoading() {
    document.getElementById('loadingOverlay').style.display = 'none';
}

// إرسال طلب API مع المصادقة
async function apiRequest(url, options = {}) {
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authToken}`
        }
    };
    
    const mergedOptions = {
        ...defaultOptions,
        ...options,
        headers: {
            ...defaultOptions.headers,
            ...options.headers
        }
    };
    
    try {
        const response = await fetch(url, mergedOptions);
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.error || 'حدث خطأ في الطلب');
        }
        
        return data;
    } catch (error) {
        console.error('API Request Error:', error);
        throw error;
    }
}

// تحميل الإحصائيات
async function loadStats() {
    try {
        showLoading();
        
        // تحميل إحصائيات الأماكن
        const placesResponse = await apiRequest('/api/places?limit=1');
        const placesCount = placesResponse.count || 0;
        document.getElementById('locationsCount').textContent = placesCount;
        
        // تحميل إحصائيات الفئات
        const categoriesResponse = await apiRequest('/api/categories');
        const categoriesCount = categoriesResponse.categories?.length || 0;
        document.getElementById('categoriesCount').textContent = categoriesCount;
        
        // تحميل إحصائيات المحافظات
        const governoratesResponse = await apiRequest('/api/governorates');
        const governoratesCount = governoratesResponse.governorates?.length || 0;
        document.getElementById('usersCount').textContent = governoratesCount;
        
        // إحصائيات وهمية للعملاء
        document.getElementById('clientsCount').textContent = '25';
        
        hideLoading();
    } catch (error) {
        console.error('Error loading stats:', error);
        hideLoading();
    }
}

// تحميل الأماكن في الجدول
async function loadLocations() {
    try {
        const response = await apiRequest('/api/places?limit=100');
        const places = response.places || [];
        
        const tableBody = document.getElementById('locationsTable');
        tableBody.innerHTML = '';
        
        places.forEach((place, index) => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${index + 1}</td>
                <td>${place.name_ar || place.name || 'غير محدد'}</td>
                <td>${place.address || 'غير محدد'}</td>
                <td>${getCategoryName(place.category_id)}</td>
                <td>${place.latitude?.toFixed(6)}, ${place.longitude?.toFixed(6)}</td>
                <td>
                    <span class="badge bg-success">نشط</span>
                </td>
                <td>
                    <button class="btn btn-sm btn-primary" onclick="editLocation(${place.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteLocation(${place.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            tableBody.appendChild(row);
        });
        
    } catch (error) {
        console.error('Error loading locations:', error);
    }
}

// تحميل الفئات
async function loadCategories() {
    try {
        const response = await apiRequest('/api/categories');
        const categories = response.categories || [];
        
        const tableBody = document.getElementById('categoriesTable');
        tableBody.innerHTML = '';
        
        categories.forEach((category, index) => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${index + 1}</td>
                <td>${category.name_ar || category.name}</td>
                <td><i class="${category.icon || 'fas fa-map-marker-alt'}"></i></td>
                <td>
                    <span class="badge" style="background-color: ${category.color || '#007bff'}">
                        ${category.color || '#007bff'}
                    </span>
                </td>
                <td>2024-01-01</td>
                <td>
                    <button class="btn btn-sm btn-primary" onclick="editCategory(${category.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteCategory(${category.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            tableBody.appendChild(row);
        });
        
    } catch (error) {
        console.error('Error loading categories:', error);
    }
}

// الحصول على اسم الفئة
function getCategoryName(categoryId) {
    const categories = {
        1: 'عام',
        2: 'مطعم',
        3: 'مستشفى',
        4: 'مدرسة',
        5: 'مسجد',
        6: 'بنك',
        7: 'محطة وقود',
        8: 'مول تجاري',
        9: 'فندق',
        10: 'صيدلية'
    };
    return categories[categoryId] || 'عام';
}

// تهيئة الخريطة
function initMap() {
    if (typeof L !== 'undefined') {
        // استخدام Leaflet
        map = L.map('map').setView([15.3694, 44.1910], 7);
        
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(map);
        
        loadLocationsOnMap();
    }
}

// تحميل الأماكن على الخريطة
async function loadLocationsOnMap() {
    try {
        const response = await apiRequest('/api/places?limit=500');
        const places = response.places || [];
        
        // مسح العلامات الحالية
        if (markers.length > 0) {
            markers.forEach(marker => map.removeLayer(marker));
            markers = [];
        }
        
        // إضافة علامات جديدة
        places.forEach(place => {
            if (place.latitude && place.longitude) {
                const marker = L.marker([place.latitude, place.longitude])
                    .addTo(map)
                    .bindPopup(`
                        <strong>${place.name_ar || place.name}</strong><br>
                        ${place.address || ''}<br>
                        <small>${getCategoryName(place.category_id)}</small>
                    `);
                
                markers.push(marker);
            }
        });
        
    } catch (error) {
        console.error('Error loading locations on map:', error);
    }
}

// تسجيل الخروج
function logout() {
    localStorage.removeItem('adminUser');
    localStorage.removeItem('yemenGpsToken');
    sessionStorage.removeItem('adminUser');
    sessionStorage.removeItem('yemenGpsToken');
    
    window.location.href = '/login';
}

// وظائف التحرير والحذف (مؤقتة)
function editLocation(id) {
    alert(`تحرير الموقع رقم: ${id}`);
}

function deleteLocation(id) {
    if (confirm('هل أنت متأكد من حذف هذا الموقع؟')) {
        alert(`حذف الموقع رقم: ${id}`);
    }
}

function editCategory(id) {
    alert(`تحرير الفئة رقم: ${id}`);
}

function deleteCategory(id) {
    if (confirm('هل أنت متأكد من حذف هذه الفئة؟')) {
        alert(`حذف الفئة رقم: ${id}`);
    }
}

// تحديث الخريطة
function refreshMap() {
    loadLocationsOnMap();
}

// إضافة موقع جديد
function addNewLocation() {
    alert('انقر على الخريطة لتحديد موقع جديد');
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من المصادقة
    if (!checkAuth()) {
        return;
    }
    
    // تحميل البيانات
    loadStats();
    loadLocations();
    loadCategories();
    
    // تهيئة الخريطة
    setTimeout(initMap, 1000);
    
    // ربط أحداث تسجيل الخروج
    document.getElementById('logoutBtn').addEventListener('click', function(e) {
        e.preventDefault();
        logout();
    });
    
    // ربط أحداث التبويبات
    document.getElementById('locations-tab').addEventListener('click', function() {
        setTimeout(loadLocations, 100);
    });
    
    document.getElementById('categories-tab').addEventListener('click', function() {
        setTimeout(loadCategories, 100);
    });
    
    document.getElementById('maps-tab').addEventListener('click', function() {
        setTimeout(function() {
            if (map) {
                map.invalidateSize();
                loadLocationsOnMap();
            }
        }, 100);
    });
});

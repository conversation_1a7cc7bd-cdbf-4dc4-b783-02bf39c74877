<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🇾🇪 Yemen Maps Pro - نظام خرائط اليمن المتقدم</title>

    <!-- Core CSS Libraries -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet-routing-machine@3.2.12/dist/leaflet-routing-machine.css" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.css" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.Default.css" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <style>
        :root {
            --primary-color: #1a73e8;
            --secondary-color: #34a853;
            --danger-color: #ea4335;
            --warning-color: #fbbc04;
            --dark-color: #202124;
            --light-color: #f8f9fa;
            --border-radius: 8px;
            --shadow: 0 2px 10px rgba(0,0,0,0.1);
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            font-family: 'Cairo', sans-serif;
            box-sizing: border-box;
        }

        body {
            margin: 0; padding: 0;
            background: var(--light-color);
            overflow: hidden;
        }

        #map {
            height: 100vh; width: 100%;
            position: relative;
        }

        /* Advanced Sidebar */
        .sidebar {
            position: fixed; top: 0; right: 0;
            width: 400px; height: 100vh;
            background: white;
            box-shadow: -4px 0 20px rgba(0,0,0,0.15);
            z-index: 1000; overflow-y: auto;
            transform: translateX(100%);
            transition: var(--transition);
            border-left: 1px solid #e8eaed;
        }
        .sidebar.open { transform: translateX(0); }

        .sidebar::-webkit-scrollbar { width: 6px; }
        .sidebar::-webkit-scrollbar-track { background: #f1f3f4; }
        .sidebar::-webkit-scrollbar-thumb {
            background: #dadce0; border-radius: 3px;
        }

        /* Enhanced Header */
        .sidebar-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #4285f4 100%);
            color: white; padding: 25px 20px; text-align: center;
            position: relative; overflow: hidden;
        }

        .sidebar-header h4 {
            margin: 0; font-size: 1.4rem; font-weight: 700;
            position: relative; z-index: 1;
        }

        .sidebar-header p {
            margin: 5px 0 0; opacity: 0.9; font-size: 0.9rem;
            position: relative; z-index: 1;
        }

        /* Advanced Search */
        .search-container {
            padding: 20px; background: #f8f9fa;
            border-bottom: 1px solid #e8eaed;
        }

        .search-box {
            position: relative; margin-bottom: 15px;
        }

        .search-box input {
            width: 100%; padding: 14px 45px 14px 15px;
            border: 2px solid #e8eaed; border-radius: var(--border-radius);
            font-size: 14px; transition: var(--transition);
            background: white;
        }

        .search-box input:focus {
            outline: none; border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(26, 115, 232, 0.1);
        }

        .search-box .search-icon {
            position: absolute; right: 15px; top: 50%;
            transform: translateY(-50%); color: #5f6368;
        }

        /* Advanced Filters */
        .filters-section {
            margin-top: 15px;
        }

        .filter-group {
            margin-bottom: 15px;
        }

        .filter-label {
            display: block; font-size: 12px; font-weight: 600;
            color: #5f6368; margin-bottom: 8px;
            text-transform: uppercase; letter-spacing: 0.5px;
        }

        .category-filters {
            display: flex; flex-wrap: wrap; gap: 8px;
        }

        .category-btn {
            padding: 8px 16px; border: 1px solid #e8eaed;
            border-radius: 20px; background: white;
            color: #5f6368; font-size: 12px; font-weight: 500;
            cursor: pointer; transition: var(--transition);
            text-decoration: none; display: inline-block;
        }

        .category-btn:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
            transform: translateY(-1px);
        }

        .category-btn.active {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        /* Enhanced Place Cards */
        .places-container {
            padding: 0;
        }

        .place-card {
            background: white; margin: 0 0 1px 0; padding: 16px;
            cursor: pointer; transition: var(--transition);
            border-bottom: 1px solid #f1f3f4;
            position: relative;
        }

        .place-card:hover {
            background: #f8f9fa;
            transform: translateX(-2px);
        }

        .place-card.active {
            background: #e8f0fe;
            border-left: 4px solid var(--primary-color);
        }

        .place-image {
            width: 60px; height: 60px; object-fit: cover;
            border-radius: var(--border-radius); float: left;
            margin-left: 12px;
        }

        .place-content {
            overflow: hidden;
        }

        .place-name {
            font-weight: 600; color: var(--dark-color);
            margin: 0 0 4px 0; font-size: 14px;
            line-height: 1.3;
        }

        .place-category {
            color: #5f6368; font-size: 12px;
            margin-bottom: 4px;
        }

        .place-rating {
            color: var(--warning-color); font-size: 12px;
            margin-bottom: 4px;
        }

        .place-address {
            color: #5f6368; font-size: 11px;
            line-height: 1.3; margin: 0;
        }

        /* Map Controls */
        .map-controls {
            position: fixed; top: 20px; left: 20px;
            z-index: 999; display: flex; flex-direction: column;
            gap: 10px;
        }

        .control-btn {
            width: 48px; height: 48px; background: white;
            border: 1px solid #e8eaed; border-radius: var(--border-radius);
            display: flex; align-items: center; justify-content: center;
            cursor: pointer; transition: var(--transition);
            box-shadow: var(--shadow);
        }

        .control-btn:hover {
            background: #f8f9fa;
            transform: scale(1.05);
        }

        .toggle-sidebar {
            position: fixed; top: 20px; right: 20px; z-index: 1001;
            background: white; border: 1px solid #e8eaed;
            border-radius: 50%; width: 56px; height: 56px;
            box-shadow: var(--shadow); cursor: pointer;
            transition: var(--transition); display: flex;
            align-items: center; justify-content: center;
        }

        .toggle-sidebar:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }

        /* Loading States */
        .loading {
            text-align: center; padding: 40px 20px;
            color: #5f6368;
        }

        .loading i {
            font-size: 24px; margin-bottom: 10px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Stats Bar */
        .stats-bar {
            position: fixed; bottom: 0; left: 0; right: 0;
            background: rgba(255,255,255,0.95); padding: 12px 20px;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1); z-index: 999;
            display: flex; justify-content: space-around; text-align: center;
            backdrop-filter: blur(10px);
        }

        .stat-item { flex: 1; }
        .stat-number {
            font-weight: 700; font-size: 18px;
            color: var(--primary-color);
        }
        .stat-label {
            font-size: 11px; color: #5f6368;
            margin-top: 2px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .sidebar { width: 100%; }
            .map-controls { top: 80px; }
            .toggle-sidebar { top: 20px; right: 20px; }
        }
    </style>
</head>
<body>
    <!-- Map Controls -->
    <div class="map-controls">
        <div class="control-btn" onclick="zoomIn()" title="تكبير">
            <i class="fas fa-plus"></i>
        </div>
        <div class="control-btn" onclick="zoomOut()" title="تصغير">
            <i class="fas fa-minus"></i>
        </div>
        <div class="control-btn" onclick="getCurrentLocation()" title="موقعي الحالي">
            <i class="fas fa-location-arrow"></i>
        </div>
        <div class="control-btn" onclick="toggleMapType()" title="نوع الخريطة">
            <i class="fas fa-layer-group"></i>
        </div>
    </div>

    <!-- Toggle Sidebar Button -->
    <button class="toggle-sidebar" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>

    <!-- Main Map -->
    <div id="map"></div>

    <!-- Advanced Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h4><i class="fas fa-map-marked-alt"></i> Yemen Maps Pro</h4>
            <p>نظام خرائط اليمن المتقدم</p>
        </div>

        <div class="search-container">
            <div class="search-box">
                <input type="text" id="searchInput" placeholder="ابحث عن مكان، مطعم، مستشفى...">
                <i class="fas fa-search search-icon"></i>
            </div>

            <div class="filters-section">
                <div class="filter-group">
                    <label class="filter-label">الفئات</label>
                    <div class="category-filters" id="categoryButtons">
                        <span class="category-btn active" data-category="">الكل</span>
                        <span class="category-btn" data-category="restaurant">مطاعم</span>
                        <span class="category-btn" data-category="hospital">مستشفيات</span>
                        <span class="category-btn" data-category="school">مدارس</span>
                        <span class="category-btn" data-category="mosque">مساجد</span>
                        <span class="category-btn" data-category="bank">بنوك</span>
                        <span class="category-btn" data-category="gas_station">محطات وقود</span>
                        <span class="category-btn" data-category="shopping_mall">مراكز تجارية</span>
                        <span class="category-btn" data-category="hotel">فنادق</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="places-container" id="placesList">
            <div class="loading">
                <i class="fas fa-spinner"></i>
                <p>جاري تحميل الأماكن...</p>
            </div>
        </div>
    </div>

    <!-- Stats Bar -->
    <div class="stats-bar">
        <div class="stat-item">
            <div class="stat-number" id="totalPlaces">0</div>
            <div class="stat-label">إجمالي الأماكن</div>
        </div>
        <div class="stat-item">
            <div class="stat-number" id="visiblePlaces">0</div>
            <div class="stat-label">الأماكن المعروضة</div>
        </div>
        <div class="stat-item">
            <div class="stat-number" id="currentGovernorate">صنعاء</div>
            <div class="stat-label">المحافظة الحالية</div>
        </div>
        <div class="stat-item">
            <div class="stat-number" id="mapZoom">12</div>
            <div class="stat-label">مستوى التكبير</div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="https://unpkg.com/leaflet-routing-machine@3.2.12/dist/leaflet-routing-machine.js"></script>
    <script src="https://unpkg.com/leaflet.markercluster@1.4.1/dist/leaflet.markercluster.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript -->
    <script src="/js/advanced-maps.js"></script>

    <style>
        /* Additional animations */
        @keyframes slideDown {
            from { transform: translateX(-50%) translateY(-100%); opacity: 0; }
            to { transform: translateX(-50%) translateY(0); opacity: 1; }
        }

        @keyframes slideUp {
            from { transform: translateX(-50%) translateY(0); opacity: 1; }
            to { transform: translateX(-50%) translateY(-100%); opacity: 0; }
        }

        /* Custom popup styles */
        .leaflet-popup-content-wrapper {
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }

        .leaflet-popup-content {
            margin: 0;
            padding: 0;
        }

        .popup-content {
            padding: 16px;
        }

        /* User location marker */
        .user-location-marker {
            background: white;
            border: 2px solid var(--primary-color);
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Marker cluster styles */
        .marker-cluster-small {
            background-color: rgba(26, 115, 232, 0.6);
        }
        .marker-cluster-small div {
            background-color: rgba(26, 115, 232, 0.8);
        }

        .marker-cluster-medium {
            background-color: rgba(52, 168, 83, 0.6);
        }
        .marker-cluster-medium div {
            background-color: rgba(52, 168, 83, 0.8);
        }

        .marker-cluster-large {
            background-color: rgba(234, 67, 53, 0.6);
        }
        .marker-cluster-large div {
            background-color: rgba(234, 67, 53, 0.8);
        }
    </style>
</body>
</html>

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Yemen Maps Pro - Advanced APIs
واجهات برمجة التطبيقات المتقدمة
"""

import os
import json
import requests
import time
from datetime import datetime, timedelta
import psycopg2
import psycopg2.extras
from pathlib import Path
import uuid
import shutil
from PIL import Image
import io
import base64

class AdvancedAPIs:
    def __init__(self, db_config):
        self.db_config = db_config
        self.conn = None
        self.google_api_key = None
        self.maptiler_api_key = None
        
    def connect_database(self):
        """الاتصال بقاعدة البيانات"""
        try:
            self.conn = psycopg2.connect(**self.db_config)
            return True
        except Exception as e:
            print(f"Database connection error: {e}")
            return False
    
    def execute_query(self, query, params=None, fetch=True):
        """تنفيذ استعلام قاعدة البيانات"""
        try:
            if not self.conn or self.conn.closed:
                self.connect_database()
            
            cursor = self.conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            cursor.execute(query, params)
            
            if fetch:
                return cursor.fetchall()
            else:
                self.conn.commit()
                return True
                
        except Exception as e:
            print(f"Query execution error: {e}")
            if self.conn:
                self.conn.rollback()
            return [] if fetch else False
    
    def get_advanced_places(self, filters=None):
        """جلب الأماكن مع فلاتر متقدمة"""
        base_query = """
            SELECT 
                p.id, p.place_id, p.name, p.name_ar,
                p.latitude, p.longitude, p.category, p.subcategory,
                p.rating, p.phone, p.address, p.website,
                p.opening_hours, p.price_level, p.is_verified,
                l.name_ar as governorate_name,
                COUNT(pp.id) as photos_count,
                AVG(pr.rating) as avg_rating,
                COUNT(pr.id) as reviews_count
            FROM places p
            LEFT JOIN locations l ON p.governorate_id = l.id
            LEFT JOIN place_photos pp ON p.place_id = pp.place_id AND pp.is_active = true
            LEFT JOIN place_reviews pr ON p.place_id = pr.place_id AND pr.is_active = true
            WHERE p.is_active = true
        """
        
        params = []
        conditions = []
        
        if filters:
            # فلترة حسب الفئة
            if filters.get('category'):
                conditions.append("p.category = %s")
                params.append(filters['category'])
            
            # فلترة حسب التقييم
            if filters.get('min_rating'):
                conditions.append("p.rating >= %s")
                params.append(filters['min_rating'])
            
            # فلترة حسب المحافظة
            if filters.get('governorate_id'):
                conditions.append("p.governorate_id = %s")
                params.append(filters['governorate_id'])
            
            # البحث النصي
            if filters.get('search'):
                conditions.append("(p.name ILIKE %s OR p.name_ar ILIKE %s OR p.address ILIKE %s)")
                search_term = f"%{filters['search']}%"
                params.extend([search_term, search_term, search_term])
            
            # فلترة حسب الموقع الجغرافي
            if filters.get('lat') and filters.get('lng') and filters.get('radius'):
                conditions.append("""
                    ST_DWithin(
                        ST_SetSRID(ST_MakePoint(p.longitude, p.latitude), 4326),
                        ST_SetSRID(ST_MakePoint(%s, %s), 4326),
                        %s
                    )
                """)
                params.extend([filters['lng'], filters['lat'], filters['radius'] * 1000])
        
        if conditions:
            base_query += " AND " + " AND ".join(conditions)
        
        base_query += """
            GROUP BY p.id, p.place_id, p.name, p.name_ar, p.latitude, p.longitude,
                     p.category, p.subcategory, p.rating, p.phone, p.address, p.website,
                     p.opening_hours, p.price_level, p.is_verified, l.name_ar
            ORDER BY p.rating DESC NULLS LAST, reviews_count DESC
        """
        
        # إضافة حد للنتائج
        if filters and filters.get('limit'):
            base_query += " LIMIT %s"
            params.append(filters['limit'])
        
        return self.execute_query(base_query, params)
    
    def get_place_details(self, place_id):
        """جلب تفاصيل مكان محدد مع جميع البيانات"""
        place_query = """
            SELECT p.*, l.name_ar as governorate_name,
                   COUNT(DISTINCT pp.id) as photos_count,
                   COUNT(DISTINCT pr.id) as reviews_count,
                   AVG(pr.rating) as avg_rating
            FROM places p
            LEFT JOIN locations l ON p.governorate_id = l.id
            LEFT JOIN place_photos pp ON p.place_id = pp.place_id AND pp.is_active = true
            LEFT JOIN place_reviews pr ON p.place_id = pr.place_id AND pr.is_active = true
            WHERE p.place_id = %s AND p.is_active = true
            GROUP BY p.id, l.name_ar
        """
        
        places = self.execute_query(place_query, [place_id])
        if not places:
            return None
        
        place = dict(places[0])
        
        # جلب الصور
        photos_query = """
            SELECT photo_path, photo_type, is_primary, display_order, caption
            FROM place_photos
            WHERE place_id = %s AND is_active = true
            ORDER BY is_primary DESC, display_order ASC
        """
        place['photos'] = self.execute_query(photos_query, [place_id])
        
        # جلب المراجعات
        reviews_query = """
            SELECT pr.*, u.username, u.full_name
            FROM place_reviews pr
            LEFT JOIN users u ON pr.user_id = u.id
            WHERE pr.place_id = %s AND pr.is_active = true
            ORDER BY pr.created_at DESC
            LIMIT 10
        """
        place['reviews'] = self.execute_query(reviews_query, [place_id])
        
        # جلب الأماكن القريبة
        nearby_query = """
            SELECT p.place_id, p.name, p.name_ar, p.latitude, p.longitude,
                   p.category, p.rating,
                   ST_Distance(
                       ST_SetSRID(ST_MakePoint(p.longitude, p.latitude), 4326),
                       ST_SetSRID(ST_MakePoint(%s, %s), 4326)
                   ) as distance
            FROM places p
            WHERE p.place_id != %s AND p.is_active = true
            AND ST_DWithin(
                ST_SetSRID(ST_MakePoint(p.longitude, p.latitude), 4326),
                ST_SetSRID(ST_MakePoint(%s, %s), 4326),
                2000
            )
            ORDER BY distance
            LIMIT 5
        """
        place['nearby_places'] = self.execute_query(nearby_query, [
            place['longitude'], place['latitude'], place_id,
            place['longitude'], place['latitude']
        ])
        
        return place
    
    def get_advanced_stats(self):
        """إحصائيات متقدمة للنظام"""
        stats = {}
        
        # إحصائيات عامة
        general_stats = self.execute_query("""
            SELECT 
                (SELECT COUNT(*) FROM places WHERE is_active = true) as total_places,
                (SELECT COUNT(*) FROM place_photos WHERE is_active = true) as total_photos,
                (SELECT COUNT(*) FROM place_reviews WHERE is_active = true) as total_reviews,
                (SELECT COUNT(DISTINCT category) FROM places WHERE is_active = true) as total_categories,
                (SELECT COUNT(*) FROM locations WHERE type = 'governorate') as total_governorates,
                (SELECT AVG(rating) FROM places WHERE rating IS NOT NULL AND is_active = true) as avg_rating
        """)
        
        if general_stats:
            stats.update(dict(general_stats[0]))
        
        # الأماكن حسب الفئة
        categories_stats = self.execute_query("""
            SELECT category, COUNT(*) as count
            FROM places 
            WHERE is_active = true AND category IS NOT NULL
            GROUP BY category
            ORDER BY count DESC
            LIMIT 10
        """)
        stats['categories'] = [dict(cat) for cat in categories_stats]
        
        # الأماكن حسب المحافظة
        governorates_stats = self.execute_query("""
            SELECT l.name_ar, COUNT(p.id) as count
            FROM locations l
            LEFT JOIN places p ON p.governorate_id = l.id AND p.is_active = true
            WHERE l.type = 'governorate'
            GROUP BY l.id, l.name_ar
            ORDER BY count DESC
        """)
        stats['governorates'] = [dict(gov) for gov in governorates_stats]
        
        # إحصائيات التقييمات
        ratings_stats = self.execute_query("""
            SELECT 
                FLOOR(rating) as rating_level,
                COUNT(*) as count
            FROM places 
            WHERE rating IS NOT NULL AND is_active = true
            GROUP BY FLOOR(rating)
            ORDER BY rating_level DESC
        """)
        stats['ratings_distribution'] = [dict(r) for r in ratings_stats]
        
        # الأماكن المضافة حديثاً (آخر 30 يوم)
        recent_stats = self.execute_query("""
            SELECT DATE(created_at) as date, COUNT(*) as count
            FROM places 
            WHERE created_at >= NOW() - INTERVAL '30 days' AND is_active = true
            GROUP BY DATE(created_at)
            ORDER BY date DESC
            LIMIT 30
        """)
        stats['recent_additions'] = [dict(r) for r in recent_stats]
        
        return stats
    
    def search_places_advanced(self, query, filters=None):
        """بحث متقدم في الأماكن"""
        search_query = """
            SELECT p.*, l.name_ar as governorate_name,
                   ts_rank(
                       to_tsvector('arabic', COALESCE(p.name, '') || ' ' || COALESCE(p.name_ar, '') || ' ' || COALESCE(p.address, '')),
                       plainto_tsquery('arabic', %s)
                   ) as rank
            FROM places p
            LEFT JOIN locations l ON p.governorate_id = l.id
            WHERE p.is_active = true
            AND (
                to_tsvector('arabic', COALESCE(p.name, '') || ' ' || COALESCE(p.name_ar, '') || ' ' || COALESCE(p.address, ''))
                @@ plainto_tsquery('arabic', %s)
                OR p.name ILIKE %s
                OR p.name_ar ILIKE %s
                OR p.address ILIKE %s
            )
        """
        
        params = [query, query, f"%{query}%", f"%{query}%", f"%{query}%"]
        
        # إضافة فلاتر إضافية
        if filters:
            if filters.get('category'):
                search_query += " AND p.category = %s"
                params.append(filters['category'])
            
            if filters.get('min_rating'):
                search_query += " AND p.rating >= %s"
                params.append(filters['min_rating'])
        
        search_query += " ORDER BY rank DESC, p.rating DESC NULLS LAST"
        
        if filters and filters.get('limit'):
            search_query += " LIMIT %s"
            params.append(filters['limit'])
        
        return self.execute_query(search_query, params)
    
    def add_place_review(self, place_id, user_id, rating, comment):
        """إضافة مراجعة لمكان"""
        insert_query = """
            INSERT INTO place_reviews (place_id, user_id, rating, comment, created_at, is_active)
            VALUES (%s, %s, %s, %s, NOW(), true)
            RETURNING id
        """
        
        result = self.execute_query(insert_query, [place_id, user_id, rating, comment])
        
        if result:
            # تحديث متوسط التقييم للمكان
            update_query = """
                UPDATE places 
                SET rating = (
                    SELECT AVG(rating) 
                    FROM place_reviews 
                    WHERE place_id = %s AND is_active = true
                )
                WHERE place_id = %s
            """
            self.execute_query(update_query, [place_id, place_id], fetch=False)
            
            return result[0]['id']
        
        return None
    
    def get_route_directions(self, start_lat, start_lng, end_lat, end_lng, profile='driving'):
        """حساب المسار بين نقطتين"""
        # هذه دالة أساسية - يمكن تطويرها لاحقاً مع خدمات التوجيه المتقدمة
        route_data = {
            'start': {'lat': start_lat, 'lng': start_lng},
            'end': {'lat': end_lat, 'lng': end_lng},
            'profile': profile,
            'distance': self.calculate_distance(start_lat, start_lng, end_lat, end_lng),
            'estimated_time': None,
            'waypoints': []
        }
        
        # حساب الوقت المقدر (تقريبي)
        if profile == 'driving':
            # متوسط سرعة 40 كم/ساعة في المدن
            route_data['estimated_time'] = (route_data['distance'] / 40) * 60  # بالدقائق
        elif profile == 'walking':
            # متوسط سرعة 5 كم/ساعة للمشي
            route_data['estimated_time'] = (route_data['distance'] / 5) * 60
        
        return route_data
    
    def calculate_distance(self, lat1, lng1, lat2, lng2):
        """حساب المسافة بين نقطتين (بالكيلومتر)"""
        from math import radians, cos, sin, asin, sqrt
        
        # تحويل إلى راديان
        lat1, lng1, lat2, lng2 = map(radians, [lat1, lng1, lat2, lng2])
        
        # معادلة Haversine
        dlng = lng2 - lng1
        dlat = lat2 - lat1
        a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlng/2)**2
        c = 2 * asin(sqrt(a))
        r = 6371  # نصف قطر الأرض بالكيلومتر
        
        return c * r
    
    def export_places_data(self, format='json'):
        """تصدير بيانات الأماكن"""
        places = self.execute_query("""
            SELECT p.*, l.name_ar as governorate_name
            FROM places p
            LEFT JOIN locations l ON p.governorate_id = l.id
            WHERE p.is_active = true
            ORDER BY p.id
        """)
        
        if format == 'json':
            return json.dumps([dict(place) for place in places], ensure_ascii=False, indent=2)
        elif format == 'csv':
            # يمكن إضافة تصدير CSV لاحقاً
            pass
        
        return places
    
    def backup_database(self):
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_file = f"backup_yemen_maps_{timestamp}.sql"
        
        # هذه دالة أساسية - يمكن تطويرها مع أدوات النسخ الاحتياطي
        return {
            'success': True,
            'backup_file': backup_file,
            'timestamp': timestamp,
            'message': 'تم إنشاء النسخة الاحتياطية بنجاح'
        }

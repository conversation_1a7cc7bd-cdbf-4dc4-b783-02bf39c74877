@echo off
chcp 65001 >nul
echo ========================================
echo      Yemen Maps - Quick Start
echo ========================================
echo.

echo 🚀 بدء تشغيل نظام خرائط اليمن...
echo.

REM التحقق من Python
echo 🔍 Checking Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python not installed! Please install Python 3.8+
    echo 📥 Download from: https://www.python.org/downloads/
    pause
    exit /b 1
)
echo ✅ Python is available

REM التحقق من PostgreSQL
echo 🔍 Checking PostgreSQL...
psql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ PostgreSQL not available in PATH
    echo ⚠️ Make sure PostgreSQL is installed and added to PATH
    echo 📍 Usually located at: C:\Program Files\PostgreSQL\15\bin
    echo.
    echo Do you want to continue? (y/n)
    set /p continue=
    if /i not "%continue%"=="y" exit /b 1
)
echo ✅ PostgreSQL is available

echo.
echo 📁 Setting up project structure...
if not exist "data" call setup_project.bat

echo.
echo 📦 Installing Python dependencies...

REM إنشاء بيئة افتراضية إذا لم تكن موجودة
if not exist "venv" (
    echo 🔧 Creating virtual environment...
    python -m venv venv
    if %errorlevel% neq 0 (
        echo ❌ Failed to create virtual environment
        pause
        exit /b 1
    )
)

REM تفعيل البيئة الافتراضية
echo 🔧 Activating virtual environment...
call venv\Scripts\activate.bat

REM تحديث pip
echo 🔧 Updating pip...
python -m pip install --upgrade pip

REM تثبيت المكتبات
echo 📦 Installing dependencies...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ❌ Some packages failed to install
    echo ⚠️ Continuing with essential packages...
    pip install Flask Flask-CORS psycopg2-binary requests Pillow
)

echo.
echo 🗄️ Setting up database...
echo ⚠️ PostgreSQL password will be requested...
echo.

REM إنشاء الجداول
psql -U postgres -h localhost -p 5432 -f database\create_tables.sql
if %errorlevel% neq 0 (
    echo ❌ Failed to create database tables
    echo 💡 Please run manually:
    echo    psql -U postgres -f database\create_tables.sql
    echo.
    echo Do you want to continue? (y/n)
    set /p continue=
    if /i not "%continue%"=="y" exit /b 1
)

echo.
echo 📊 Importing existing data...
python tools\import\import_existing_data.py
if %errorlevel% neq 0 (
    echo ⚠️ Warning: Failed to import existing data
    echo 💡 Sample data will be created instead
)

echo.
echo 🌐 Starting server...
echo 🚀 Server will start on port 5000...
echo.
echo ⏱️ Server will run until you press Ctrl+C
echo 🌐 Visit: http://localhost:5000
echo 🛠️ Admin panel: http://localhost:5000/admin
echo.

REM تشغيل الخادم
python server\app.py

echo.
echo ========================================
echo ✅ Yemen Maps session ended
echo ========================================
echo.
echo 📋 To restart the server:
echo    cd yemen-maps
echo    venv\Scripts\activate
echo    python server\app.py
echo.
echo 🎉 Thank you for using Yemen Maps!
echo.
pause

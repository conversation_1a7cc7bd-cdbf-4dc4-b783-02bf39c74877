@echo off
chcp 65001 >nul
echo ========================================
echo      Yemen Maps - Data Enhancement
echo ========================================
echo.

echo 🔧 تحسين وإثراء بيانات خرائط اليمن...
echo.

REM التحقق من Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python not installed!
    pause
    exit /b 1
)

REM تفعيل البيئة الافتراضية إذا كانت موجودة
if exist "venv\Scripts\activate.bat" (
    echo 🔧 Activating virtual environment...
    call venv\Scripts\activate.bat
)

echo 📊 تشغيل أداة تحسين البيانات...
echo.

python tools\data_enhancer.py

echo.
echo ========================================
echo ✅ انتهت عملية تحسين البيانات
echo ========================================
echo.
pause

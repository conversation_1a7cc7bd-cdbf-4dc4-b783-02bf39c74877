/**
 * Yemen Maps Pro - Advanced JavaScript
 * نظام خرائط اليمن المتقدم
 */

class YemenMapsPro {
    constructor() {
        this.map = null;
        this.placesLayer = null;
        this.markersCluster = null;
        this.routingControl = null;
        this.currentPlaces = [];
        this.currentFilter = '';
        this.currentSearch = '';
        this.currentMapType = 'streets';
        this.userLocation = null;

        // Map types configuration
        this.mapTypes = {
            streets: {
                name: 'شوارع',
                url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
                attribution: '© OpenStreetMap contributors'
            },
            satellite: {
                name: 'قمر صناعي',
                url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
                attribution: '© Esri'
            },
            terrain: {
                name: 'تضاريس',
                url: 'https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png',
                attribution: '© OpenTopoMap'
            }
        };

        this.init();
    }

    init() {
        this.initMap();
        this.initEventListeners();
        this.loadPlaces();
        this.loadStats();
        this.getCurrentLocation();
    }

    initMap() {
        // Initialize map centered on Sana'a
        this.map = L.map('map', {
            center: [15.3694, 44.1910],
            zoom: 12,
            zoomControl: false,
            attributionControl: false
        });

        // Add default tile layer
        this.currentTileLayer = L.tileLayer(this.mapTypes.streets.url, {
            attribution: this.mapTypes.streets.attribution,
            maxZoom: 18
        }).addTo(this.map);

        // Initialize marker cluster
        this.markersCluster = L.markerClusterGroup({
            chunkedLoading: true,
            maxClusterRadius: 50
        });
        this.map.addLayer(this.markersCluster);

        // Map event listeners
        this.map.on('zoomend', () => {
            document.getElementById('mapZoom').textContent = this.map.getZoom();
        });

        this.map.on('moveend', () => {
            this.updateVisiblePlaces();
        });
    }

    initEventListeners() {
        // Search input
        const searchInput = document.getElementById('searchInput');
        searchInput.addEventListener('input', (e) => {
            this.searchPlaces(e.target.value);
        });

        // Category filters
        document.querySelectorAll('.category-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                this.filterByCategory(btn.dataset.category);

                // Update active state
                document.querySelectorAll('.category-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
            });
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'f') {
                e.preventDefault();
                searchInput.focus();
            }
        });
    }

    async loadPlaces() {
        try {
            this.showLoading();

            const response = await fetch('/api/places?limit=1000');
            const data = await response.json();

            if (data.success) {
                this.currentPlaces = data.places;
                this.displayPlaces(this.currentPlaces);
                this.updateStats();
            } else {
                this.showError('خطأ في تحميل البيانات: ' + data.error);
            }
        } catch (error) {
            console.error('Error loading places:', error);
            this.showError('خطأ في الاتصال بالخادم');
        }
    }

    displayPlaces(places) {
        // Clear existing markers
        this.markersCluster.clearLayers();

        const placesList = document.getElementById('placesList');
        placesList.innerHTML = '';

        if (places.length === 0) {
            placesList.innerHTML = '<div class="loading"><p>لا توجد أماكن مطابقة للبحث</p></div>';
            return;
        }

        places.forEach((place, index) => {
            // Add marker to map
            const marker = this.createMarker(place);
            this.markersCluster.addLayer(marker);

            // Add to sidebar list
            const placeCard = this.createPlaceCard(place, index);
            placesList.appendChild(placeCard);
        });

        // Update visible places count
        document.getElementById('visiblePlaces').textContent = places.length;
    }

    createMarker(place) {
        const marker = L.marker([place.latitude, place.longitude]);

        // Custom popup
        const popupContent = this.createPopupContent(place);
        marker.bindPopup(popupContent, {
            maxWidth: 300,
            className: 'custom-popup'
        });

        // Store place data
        marker.placeData = place;

        return marker;
    }

    createPopupContent(place) {
        return `
            <div class="popup-content">
                ${place.primary_photo ?
                    `<img src="${place.primary_photo}" class="popup-image" alt="${place.name}" style="width:100%;height:120px;object-fit:cover;border-radius:8px;margin-bottom:10px;">`
                    : ''
                }
                <h6 class="popup-name" style="margin:0 0 5px 0;font-weight:600;">${place.name || place.name_ar}</h6>
                <div class="popup-category" style="color:#5f6368;font-size:12px;margin-bottom:5px;">
                    <i class="fas fa-tag"></i> ${this.getCategoryName(place.category)}
                </div>
                ${place.rating ?
                    `<div class="popup-rating" style="color:#fbbc04;margin-bottom:5px;">
                        ${'★'.repeat(Math.floor(place.rating))} ${place.rating}
                    </div>`
                    : ''
                }
                ${place.address ?
                    `<div class="popup-address" style="color:#5f6368;font-size:11px;margin-bottom:10px;">
                        <i class="fas fa-map-marker-alt"></i> ${place.address}
                    </div>`
                    : ''
                }
                <div class="popup-actions" style="display:flex;gap:8px;">
                    <button class="btn btn-primary btn-sm" onclick="yemenMaps.showPlaceDetails('${place.place_id}')">
                        <i class="fas fa-info-circle"></i> التفاصيل
                    </button>
                    <button class="btn btn-success btn-sm" onclick="yemenMaps.getDirections(${place.latitude}, ${place.longitude})">
                        <i class="fas fa-directions"></i> الاتجاهات
                    </button>
                </div>
            </div>
        `;
    }

    createPlaceCard(place, index) {
        const card = document.createElement('div');
        card.className = 'place-card';
        card.dataset.placeId = place.place_id;

        card.innerHTML = `
            ${place.primary_photo ?
                `<img src="${place.primary_photo}" class="place-image" alt="${place.name}">`
                : '<div class="place-image" style="background:#f1f3f4;display:flex;align-items:center;justify-content:center;"><i class="fas fa-image" style="color:#5f6368;"></i></div>'
            }
            <div class="place-content">
                <h6 class="place-name">${place.name || place.name_ar}</h6>
                <div class="place-category">
                    <i class="fas fa-tag"></i> ${this.getCategoryName(place.category)}
                </div>
                ${place.rating ?
                    `<div class="place-rating">
                        ${'★'.repeat(Math.floor(place.rating))} ${place.rating}
                    </div>`
                    : ''
                }
                ${place.address ?
                    `<p class="place-address">${place.address}</p>`
                    : ''
                }
            </div>
        `;

        // Click event
        card.addEventListener('click', () => {
            this.selectPlace(place, card);
        });

        return card;
    }

    selectPlace(place, cardElement) {
        // Remove active class from all cards
        document.querySelectorAll('.place-card').forEach(card => {
            card.classList.remove('active');
        });

        // Add active class to selected card
        cardElement.classList.add('active');

        // Center map on place
        this.map.setView([place.latitude, place.longitude], 16);

        // Find and open marker popup
        this.markersCluster.eachLayer(marker => {
            if (marker.placeData && marker.placeData.place_id === place.place_id) {
                marker.openPopup();
            }
        });
    }

    getCategoryName(category) {
        const categories = {
            'restaurant': 'مطعم',
            'hospital': 'مستشفى',
            'school': 'مدرسة',
            'mosque': 'مسجد',
            'bank': 'بنك',
            'gas_station': 'محطة وقود',
            'shopping_mall': 'مركز تجاري',
            'hotel': 'فندق',
            'pharmacy': 'صيدلية',
            'university': 'جامعة',
            'general': 'عام'
        };
        return categories[category] || category || 'غير محدد';
    }

    filterByCategory(category) {
        this.currentFilter = category;
        const filtered = category ?
            this.currentPlaces.filter(place => place.category === category) :
            this.currentPlaces;
        this.displayPlaces(filtered);
    }

    searchPlaces(query) {
        this.currentSearch = query.toLowerCase();
        const filtered = this.currentPlaces.filter(place =>
            (place.name && place.name.toLowerCase().includes(this.currentSearch)) ||
            (place.name_ar && place.name_ar.includes(this.currentSearch)) ||
            (place.address && place.address.toLowerCase().includes(this.currentSearch)) ||
            (place.category && this.getCategoryName(place.category).includes(this.currentSearch))
        );
        this.displayPlaces(filtered);
    }

    async loadStats() {
        try {
            const response = await fetch('/api/stats');
            const data = await response.json();
            if (data.success) {
                document.getElementById('totalPlaces').textContent = data.stats.total_places || 0;
            }
        } catch (error) {
            console.error('Error loading stats:', error);
        }
    }

    updateStats() {
        document.getElementById('totalPlaces').textContent = this.currentPlaces.length;
        document.getElementById('visiblePlaces').textContent = this.currentPlaces.length;
        document.getElementById('mapZoom').textContent = this.map.getZoom();
    }

    updateVisiblePlaces() {
        const bounds = this.map.getBounds();
        let visibleCount = 0;

        this.markersCluster.eachLayer(marker => {
            if (bounds.contains(marker.getLatLng())) {
                visibleCount++;
            }
        });

        document.getElementById('visiblePlaces').textContent = visibleCount;
    }

    showLoading() {
        document.getElementById('placesList').innerHTML = `
            <div class="loading">
                <i class="fas fa-spinner"></i>
                <p>جاري تحميل الأماكن...</p>
            </div>
        `;
    }

    showError(message) {
        document.getElementById('placesList').innerHTML = `
            <div class="loading">
                <i class="fas fa-exclamation-triangle" style="color:var(--danger-color);"></i>
                <p>${message}</p>
            </div>
        `;
    }

    // Map control functions
    zoomIn() {
        this.map.zoomIn();
    }

    zoomOut() {
        this.map.zoomOut();
    }

    toggleMapType() {
        const types = Object.keys(this.mapTypes);
        const currentIndex = types.indexOf(this.currentMapType);
        const nextIndex = (currentIndex + 1) % types.length;
        const nextType = types[nextIndex];

        this.map.removeLayer(this.currentTileLayer);
        this.currentTileLayer = L.tileLayer(this.mapTypes[nextType].url, {
            attribution: this.mapTypes[nextType].attribution,
            maxZoom: 18
        }).addTo(this.map);

        this.currentMapType = nextType;

        // Show notification
        this.showNotification(`تم التبديل إلى: ${this.mapTypes[nextType].name}`);
    }

    getCurrentLocation() {
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    const lat = position.coords.latitude;
                    const lng = position.coords.longitude;

                    this.userLocation = [lat, lng];
                    this.map.setView([lat, lng], 15);

                    // Add user location marker
                    if (this.userMarker) {
                        this.map.removeLayer(this.userMarker);
                    }

                    this.userMarker = L.marker([lat, lng], {
                        icon: L.divIcon({
                            className: 'user-location-marker',
                            html: '<i class="fas fa-location-arrow" style="color:#1a73e8;"></i>',
                            iconSize: [20, 20]
                        })
                    }).addTo(this.map);

                    this.showNotification('تم تحديد موقعك الحالي');
                },
                (error) => {
                    this.showNotification('لا يمكن تحديد موقعك الحالي', 'error');
                }
            );
        } else {
            this.showNotification('المتصفح لا يدعم تحديد الموقع', 'error');
        }
    }

    showNotification(message, type = 'success') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.style.cssText = `
            position: fixed; top: 20px; left: 50%; transform: translateX(-50%);
            background: ${type === 'error' ? 'var(--danger-color)' : 'var(--secondary-color)'};
            color: white; padding: 12px 20px; border-radius: var(--border-radius);
            z-index: 10000; box-shadow: var(--shadow);
            animation: slideDown 0.3s ease;
        `;
        notification.textContent = message;

        document.body.appendChild(notification);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.animation = 'slideUp 0.3s ease';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    async showPlaceDetails(placeId) {
        try {
            const response = await fetch(`/api/place/${placeId}`);
            const data = await response.json();

            if (data.success) {
                this.displayPlaceDetailsModal(data.place);
            } else {
                this.showNotification('خطأ في تحميل تفاصيل المكان', 'error');
            }
        } catch (error) {
            console.error('Error loading place details:', error);
            this.showNotification('خطأ في الاتصال بالخادم', 'error');
        }
    }

    displayPlaceDetailsModal(place) {
        // Create modal HTML
        const modalHTML = `
            <div class="modal fade" id="placeDetailsModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">${place.name || place.name_ar}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${place.photos && place.photos.length > 0 ? `
                                <div class="place-photos mb-3">
                                    <div id="placeCarousel" class="carousel slide" data-bs-ride="carousel">
                                        <div class="carousel-inner">
                                            ${place.photos.map((photo, index) => `
                                                <div class="carousel-item ${index === 0 ? 'active' : ''}">
                                                    <img src="${photo.photo_url}" class="d-block w-100" style="height:300px;object-fit:cover;">
                                                </div>
                                            `).join('')}
                                        </div>
                                        ${place.photos.length > 1 ? `
                                            <button class="carousel-control-prev" type="button" data-bs-target="#placeCarousel" data-bs-slide="prev">
                                                <span class="carousel-control-prev-icon"></span>
                                            </button>
                                            <button class="carousel-control-next" type="button" data-bs-target="#placeCarousel" data-bs-slide="next">
                                                <span class="carousel-control-next-icon"></span>
                                            </button>
                                        ` : ''}
                                    </div>
                                </div>
                            ` : ''}

                            <div class="place-info">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6><i class="fas fa-tag"></i> الفئة</h6>
                                        <p>${this.getCategoryName(place.category)}</p>

                                        ${place.rating ? `
                                            <h6><i class="fas fa-star"></i> التقييم</h6>
                                            <p>${'★'.repeat(Math.floor(place.rating))} ${place.rating}/5</p>
                                        ` : ''}

                                        ${place.phone ? `
                                            <h6><i class="fas fa-phone"></i> الهاتف</h6>
                                            <p><a href="tel:${place.phone}">${place.phone}</a></p>
                                        ` : ''}
                                    </div>
                                    <div class="col-md-6">
                                        ${place.address ? `
                                            <h6><i class="fas fa-map-marker-alt"></i> العنوان</h6>
                                            <p>${place.address}</p>
                                        ` : ''}

                                        ${place.governorate_name ? `
                                            <h6><i class="fas fa-map"></i> المحافظة</h6>
                                            <p>${place.governorate_name}</p>
                                        ` : ''}

                                        ${place.website ? `
                                            <h6><i class="fas fa-globe"></i> الموقع الإلكتروني</h6>
                                            <p><a href="${place.website}" target="_blank">${place.website}</a></p>
                                        ` : ''}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-success" onclick="yemenMaps.getDirections(${place.latitude}, ${place.longitude})">
                                <i class="fas fa-directions"></i> الحصول على الاتجاهات
                            </button>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal
        const existingModal = document.getElementById('placeDetailsModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('placeDetailsModal'));
        modal.show();
    }

    async getDirections(lat, lng) {
        if (!this.userLocation) {
            this.showNotification('يرجى تحديد موقعك الحالي أولاً', 'error');
            this.getCurrentLocation();
            return;
        }

        try {
            // Remove existing routing control
            if (this.routingControl) {
                this.map.removeControl(this.routingControl);
            }

            // Create new routing control
            this.routingControl = L.Routing.control({
                waypoints: [
                    L.latLng(this.userLocation[0], this.userLocation[1]),
                    L.latLng(lat, lng)
                ],
                routeWhileDragging: true,
                addWaypoints: false,
                createMarker: function() { return null; }, // Don't create default markers
                lineOptions: {
                    styles: [{ color: '#1a73e8', weight: 6, opacity: 0.8 }]
                },
                language: 'ar'
            }).addTo(this.map);

            this.showNotification('تم إنشاء المسار بنجاح');

        } catch (error) {
            console.error('Error creating directions:', error);
            this.showNotification('خطأ في إنشاء المسار', 'error');
        }
    }
}

// Global functions for HTML onclick events
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    sidebar.classList.toggle('open');
}

function zoomIn() {
    if (window.yemenMaps) window.yemenMaps.zoomIn();
}

function zoomOut() {
    if (window.yemenMaps) window.yemenMaps.zoomOut();
}

function getCurrentLocation() {
    if (window.yemenMaps) window.yemenMaps.getCurrentLocation();
}

function toggleMapType() {
    if (window.yemenMaps) window.yemenMaps.toggleMapType();
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.yemenMaps = new YemenMapsPro();
});

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Yemen Maps Pro - Advanced Routes
المسارات المتقدمة للنظام
"""

from flask import request, jsonify
import logging

logger = logging.getLogger(__name__)

def register_advanced_routes(app, advanced_apis):
    """تسجيل المسارات المتقدمة"""
    
    @app.route('/api/places/advanced')
    def get_places_advanced():
        """جلب الأماكن مع فلاتر متقدمة"""
        try:
            filters = {
                'category': request.args.get('category'),
                'min_rating': request.args.get('min_rating', type=float),
                'governorate_id': request.args.get('governorate_id', type=int),
                'search': request.args.get('search'),
                'lat': request.args.get('lat', type=float),
                'lng': request.args.get('lng', type=float),
                'radius': request.args.get('radius', 10, type=float),
                'limit': request.args.get('limit', 100, type=int)
            }
            
            # إزالة القيم الفارغة
            filters = {k: v for k, v in filters.items() if v is not None}
            
            advanced_apis.connect_database()
            places = advanced_apis.get_advanced_places(filters)
            
            return jsonify({
                'success': True,
                'places': [dict(place) for place in places],
                'count': len(places)
            })
            
        except Exception as e:
            logger.error(f"Error getting advanced places: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @app.route('/api/places/search')
    def search_places_advanced():
        """بحث متقدم في الأماكن"""
        try:
            query = request.args.get('q', '')
            if not query:
                return jsonify({'success': False, 'error': 'Search query is required'}), 400
            
            filters = {
                'category': request.args.get('category'),
                'min_rating': request.args.get('min_rating', type=float),
                'limit': request.args.get('limit', 50, type=int)
            }
            
            # إزالة القيم الفارغة
            filters = {k: v for k, v in filters.items() if v is not None}
            
            advanced_apis.connect_database()
            places = advanced_apis.search_places_advanced(query, filters)
            
            return jsonify({
                'success': True,
                'places': [dict(place) for place in places],
                'count': len(places),
                'query': query
            })
            
        except Exception as e:
            logger.error(f"Error searching places: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @app.route('/api/places/<place_id>/details')
    def get_place_details_advanced(place_id):
        """جلب تفاصيل مكان محدد مع جميع البيانات"""
        try:
            advanced_apis.connect_database()
            place = advanced_apis.get_place_details(place_id)
            
            if not place:
                return jsonify({'success': False, 'error': 'Place not found'}), 404
            
            return jsonify({
                'success': True,
                'place': place
            })
            
        except Exception as e:
            logger.error(f"Error getting place details: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @app.route('/api/places/<place_id>/reviews', methods=['POST'])
    def add_place_review(place_id):
        """إضافة مراجعة لمكان"""
        try:
            data = request.get_json()
            
            if not data or 'rating' not in data:
                return jsonify({'success': False, 'error': 'Rating is required'}), 400
            
            rating = data.get('rating')
            comment = data.get('comment', '')
            user_id = data.get('user_id', 1)  # مؤقت - يجب الحصول من المصادقة
            
            if not (1 <= rating <= 5):
                return jsonify({'success': False, 'error': 'Rating must be between 1 and 5'}), 400
            
            advanced_apis.connect_database()
            review_id = advanced_apis.add_place_review(place_id, user_id, rating, comment)
            
            if review_id:
                return jsonify({
                    'success': True,
                    'review_id': review_id,
                    'message': 'Review added successfully'
                })
            else:
                return jsonify({'success': False, 'error': 'Failed to add review'}), 500
            
        except Exception as e:
            logger.error(f"Error adding review: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @app.route('/api/routes/directions')
    def get_directions():
        """حساب المسار بين نقطتين"""
        try:
            start_lat = request.args.get('start_lat', type=float)
            start_lng = request.args.get('start_lng', type=float)
            end_lat = request.args.get('end_lat', type=float)
            end_lng = request.args.get('end_lng', type=float)
            profile = request.args.get('profile', 'driving')
            
            if not all([start_lat, start_lng, end_lat, end_lng]):
                return jsonify({'success': False, 'error': 'Start and end coordinates are required'}), 400
            
            advanced_apis.connect_database()
            route = advanced_apis.get_route_directions(start_lat, start_lng, end_lat, end_lng, profile)
            
            return jsonify({
                'success': True,
                'route': route
            })
            
        except Exception as e:
            logger.error(f"Error getting directions: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @app.route('/api/export/places')
    def export_places():
        """تصدير بيانات الأماكن"""
        try:
            format_type = request.args.get('format', 'json')
            
            advanced_apis.connect_database()
            data = advanced_apis.export_places_data(format_type)
            
            if format_type == 'json':
                return jsonify({
                    'success': True,
                    'data': data
                })
            
            return jsonify({'success': False, 'error': 'Unsupported format'}), 400
            
        except Exception as e:
            logger.error(f"Error exporting places: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @app.route('/api/admin/backup', methods=['POST'])
    def create_backup():
        """إنشاء نسخة احتياطية"""
        try:
            advanced_apis.connect_database()
            result = advanced_apis.backup_database()
            
            return jsonify({
                'success': True,
                'backup': result
            })
            
        except Exception as e:
            logger.error(f"Error creating backup: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @app.route('/api/places/nearby')
    def get_nearby_places():
        """جلب الأماكن القريبة"""
        try:
            lat = request.args.get('lat', type=float)
            lng = request.args.get('lng', type=float)
            radius = request.args.get('radius', 5, type=float)
            category = request.args.get('category')
            limit = request.args.get('limit', 20, type=int)
            
            if not lat or not lng:
                return jsonify({'success': False, 'error': 'Latitude and longitude are required'}), 400
            
            filters = {
                'lat': lat,
                'lng': lng,
                'radius': radius,
                'limit': limit
            }
            
            if category:
                filters['category'] = category
            
            advanced_apis.connect_database()
            places = advanced_apis.get_advanced_places(filters)
            
            return jsonify({
                'success': True,
                'places': [dict(place) for place in places],
                'count': len(places),
                'center': {'lat': lat, 'lng': lng},
                'radius': radius
            })
            
        except Exception as e:
            logger.error(f"Error getting nearby places: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @app.route('/api/places/trending')
    def get_trending_places():
        """جلب الأماكن الرائجة"""
        try:
            limit = request.args.get('limit', 10, type=int)
            
            # استعلام للأماكن الأكثر تقييماً وزيارة
            query = """
                SELECT p.*, l.name_ar as governorate_name,
                       COUNT(pr.id) as reviews_count,
                       AVG(pr.rating) as avg_rating
                FROM places p
                LEFT JOIN locations l ON p.governorate_id = l.id
                LEFT JOIN place_reviews pr ON p.place_id = pr.place_id AND pr.is_active = true
                WHERE p.is_active = true
                GROUP BY p.id, l.name_ar
                HAVING COUNT(pr.id) > 0
                ORDER BY avg_rating DESC, reviews_count DESC
                LIMIT %s
            """
            
            advanced_apis.connect_database()
            places = advanced_apis.execute_query(query, [limit])
            
            return jsonify({
                'success': True,
                'places': [dict(place) for place in places],
                'count': len(places)
            })
            
        except Exception as e:
            logger.error(f"Error getting trending places: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @app.route('/api/places/recent')
    def get_recent_places():
        """جلب الأماكن المضافة حديثاً"""
        try:
            limit = request.args.get('limit', 10, type=int)
            days = request.args.get('days', 30, type=int)
            
            query = """
                SELECT p.*, l.name_ar as governorate_name
                FROM places p
                LEFT JOIN locations l ON p.governorate_id = l.id
                WHERE p.is_active = true
                AND p.created_at >= NOW() - INTERVAL '%s days'
                ORDER BY p.created_at DESC
                LIMIT %s
            """
            
            advanced_apis.connect_database()
            places = advanced_apis.execute_query(query, [days, limit])
            
            return jsonify({
                'success': True,
                'places': [dict(place) for place in places],
                'count': len(places),
                'period_days': days
            })
            
        except Exception as e:
            logger.error(f"Error getting recent places: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @app.route('/api/analytics/dashboard')
    def get_analytics_dashboard():
        """جلب بيانات لوحة التحكم التحليلية"""
        try:
            advanced_apis.connect_database()
            
            # إحصائيات شاملة
            stats = advanced_apis.get_advanced_stats()
            
            # إضافة إحصائيات إضافية
            additional_stats = {}
            
            # أكثر الفئات شعبية
            popular_categories = advanced_apis.execute_query("""
                SELECT category, COUNT(*) as count,
                       AVG(rating) as avg_rating
                FROM places 
                WHERE is_active = true AND category IS NOT NULL
                GROUP BY category
                ORDER BY count DESC, avg_rating DESC
                LIMIT 5
            """)
            additional_stats['popular_categories'] = [dict(cat) for cat in popular_categories]
            
            # أعلى المحافظات تقييماً
            top_governorates = advanced_apis.execute_query("""
                SELECT l.name_ar, COUNT(p.id) as places_count,
                       AVG(p.rating) as avg_rating
                FROM locations l
                LEFT JOIN places p ON p.governorate_id = l.id AND p.is_active = true
                WHERE l.type = 'governorate' AND p.rating IS NOT NULL
                GROUP BY l.id, l.name_ar
                HAVING COUNT(p.id) > 0
                ORDER BY avg_rating DESC, places_count DESC
                LIMIT 5
            """)
            additional_stats['top_governorates'] = [dict(gov) for gov in top_governorates]
            
            # دمج الإحصائيات
            stats.update(additional_stats)
            
            return jsonify({
                'success': True,
                'analytics': stats
            })
            
        except Exception as e:
            logger.error(f"Error getting analytics dashboard: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    return app

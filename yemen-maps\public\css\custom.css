/* 
 * Yemen GPS Admin Dashboard Custom Styles
 * أنماط مخصصة للوحة تحكم يمن GPS
 */

/* الخطوط والألوان الأساسية */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 8px;
    --box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    --transition: all 0.3s ease;
}

/* الخط العربي */
body {
    font-family: 'Tajawal', 'Arial', sans-serif;
    background-color: var(--light-color);
    direction: rtl;
    text-align: right;
}

/* شريط التنقل */
.navbar {
    box-shadow: var(--box-shadow);
    background: linear-gradient(135deg, var(--primary-color), var(--dark-color)) !important;
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.2rem;
}

.navbar-brand img {
    width: 32px;
    height: 32px;
    margin-left: 10px;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-left: 8px;
}

/* البطاقات */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    margin-bottom: 20px;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
    font-weight: 500;
}

/* بطاقات الإحصائيات */
.card.bg-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
}

.card.bg-success {
    background: linear-gradient(135deg, var(--success-color), #2ecc71) !important;
}

.card.bg-info {
    background: linear-gradient(135deg, var(--info-color), #3498db) !important;
}

.card.bg-warning {
    background: linear-gradient(135deg, var(--warning-color), #e67e22) !important;
}

/* الجداول */
.table {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table thead th {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    font-weight: 500;
    padding: 15px;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: rgba(52, 152, 219, 0.1);
    transform: scale(1.01);
}

.table tbody td {
    padding: 12px 15px;
    vertical-align: middle;
    border-color: #e9ecef;
}

/* الأزرار */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
    padding: 8px 16px;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.btn-add {
    margin-left: 10px;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: none;
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color), #2ecc71);
    border: none;
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color), #e67e22);
    border: none;
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color), #c0392b);
    border: none;
}

.btn-info {
    background: linear-gradient(135deg, var(--info-color), #3498db);
    border: none;
}

/* علامات التبويب */
.nav-tabs {
    border-bottom: 2px solid var(--primary-color);
}

.nav-tabs .nav-link {
    border: none;
    color: var(--primary-color);
    font-weight: 500;
    transition: var(--transition);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    margin-left: 5px;
}

.nav-tabs .nav-link:hover {
    background-color: rgba(52, 152, 219, 0.1);
    border-color: transparent;
}

.nav-tabs .nav-link.active {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-color: transparent;
}

/* النماذج */
.form-control {
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
    transition: var(--transition);
    padding: 10px 15px;
}

.form-control:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.form-label {
    font-weight: 500;
    color: var(--primary-color);
    margin-bottom: 8px;
}

/* النوافذ المنبثقة */
.modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    padding: 15px 20px;
}

/* مؤشر التحميل */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    visibility: hidden;
    opacity: 0;
    transition: var(--transition);
}

.loading-overlay.show {
    visibility: visible;
    opacity: 1;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid var(--secondary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* الخريطة */
#map {
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    border: 2px solid #e9ecef;
}

/* شارات الحالة */
.badge {
    font-size: 0.75rem;
    padding: 5px 10px;
    border-radius: 20px;
}

.badge.bg-success {
    background: linear-gradient(135deg, var(--success-color), #2ecc71) !important;
}

.badge.bg-danger {
    background: linear-gradient(135deg, var(--danger-color), #c0392b) !important;
}

.badge.bg-warning {
    background: linear-gradient(135deg, var(--warning-color), #e67e22) !important;
}

/* التنبيهات */
.alert {
    border: none;
    border-radius: var(--border-radius);
    font-weight: 500;
}

.alert-success {
    background: linear-gradient(135deg, rgba(39, 174, 96, 0.1), rgba(46, 204, 113, 0.1));
    color: var(--success-color);
    border-right: 4px solid var(--success-color);
}

.alert-danger {
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.1), rgba(192, 57, 43, 0.1));
    color: var(--danger-color);
    border-right: 4px solid var(--danger-color);
}

.alert-warning {
    background: linear-gradient(135deg, rgba(243, 156, 18, 0.1), rgba(230, 126, 34, 0.1));
    color: var(--warning-color);
    border-right: 4px solid var(--warning-color);
}

.alert-info {
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.1), rgba(52, 152, 219, 0.1));
    color: var(--info-color);
    border-right: 4px solid var(--info-color);
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .card {
        margin-bottom: 15px;
    }
    
    .table-responsive {
        border-radius: var(--border-radius);
    }
    
    .btn {
        margin-bottom: 5px;
    }
    
    .nav-tabs .nav-link {
        font-size: 0.9rem;
        padding: 8px 12px;
    }
    
    .card-header {
        padding: 15px;
    }
    
    .card-body {
        padding: 15px;
    }
}

/* تحسينات إضافية */
.text-muted {
    color: #6c757d !important;
}

.border-radius {
    border-radius: var(--border-radius) !important;
}

.shadow {
    box-shadow: var(--box-shadow) !important;
}

.transition {
    transition: var(--transition) !important;
}

/* أيقونات ملونة */
.text-primary { color: var(--primary-color) !important; }
.text-success { color: var(--success-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-danger { color: var(--danger-color) !important; }
.text-info { color: var(--info-color) !important; }

/* تحسين المظهر العام */
.container-fluid {
    padding: 20px;
}

.row {
    margin-bottom: 20px;
}

/* تأثيرات التمرير */
.card, .btn, .form-control, .nav-link {
    transition: var(--transition);
}

/* تحسين الطباعة */
@media print {
    .navbar, .btn, .loading-overlay {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Yemen Maps Pro - Complete System Installer
أداة التثبيت الكاملة للنظام
"""

import os
import sys
import json
import subprocess
import time
from pathlib import Path
import logging

# إعداد المسارات
BASE_DIR = Path(__file__).parent.parent

# إعداد السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(BASE_DIR / 'logs' / 'system_installer.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SystemInstaller:
    def __init__(self):
        self.base_dir = BASE_DIR
        self.installation_steps = []
        self.completed_steps = []
        self.failed_steps = []
        
        # إنشاء مجلد السجلات
        (self.base_dir / 'logs').mkdir(exist_ok=True)
    
    def log_step(self, step_name, status, details=""):
        """تسجيل خطوة التثبيت"""
        step_info = {
            'step': step_name,
            'status': status,
            'details': details,
            'timestamp': time.time()
        }
        
        if status == 'completed':
            self.completed_steps.append(step_info)
            logger.info(f"✅ {step_name}")
        elif status == 'failed':
            self.failed_steps.append(step_info)
            logger.error(f"❌ {step_name}: {details}")
        else:
            logger.info(f"🔄 {step_name}")
    
    def check_python_requirements(self):
        """فحص متطلبات Python"""
        self.log_step("Checking Python requirements", "running")
        
        try:
            # فحص إصدار Python
            python_version = sys.version_info
            if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 7):
                self.log_step("Python version check", "failed", "Python 3.7+ required")
                return False
            
            # فحص المكتبات المطلوبة
            required_packages = [
                'flask', 'psycopg2', 'requests', 'pillow', 'flask_cors'
            ]
            
            missing_packages = []
            for package in required_packages:
                try:
                    __import__(package)
                except ImportError:
                    missing_packages.append(package)
            
            if missing_packages:
                self.log_step("Python packages check", "failed", f"Missing: {', '.join(missing_packages)}")
                return False
            
            self.log_step("Python requirements check", "completed")
            return True
            
        except Exception as e:
            self.log_step("Python requirements check", "failed", str(e))
            return False
    
    def install_python_packages(self):
        """تثبيت مكتبات Python المطلوبة"""
        self.log_step("Installing Python packages", "running")
        
        try:
            packages = [
                'flask>=2.0.0',
                'psycopg2-binary>=2.9.0',
                'requests>=2.25.0',
                'pillow>=8.0.0',
                'flask-cors>=3.0.0'
            ]
            
            for package in packages:
                result = subprocess.run([
                    sys.executable, '-m', 'pip', 'install', package
                ], capture_output=True, text=True)
                
                if result.returncode != 0:
                    self.log_step("Package installation", "failed", f"Failed to install {package}")
                    return False
            
            self.log_step("Python packages installation", "completed")
            return True
            
        except Exception as e:
            self.log_step("Python packages installation", "failed", str(e))
            return False
    
    def setup_directory_structure(self):
        """إعداد هيكل المجلدات"""
        self.log_step("Setting up directory structure", "running")
        
        try:
            directories = [
                'images/places',
                'images/imported',
                'images/temp',
                'public/assets',
                'public/css',
                'public/js',
                'logs',
                'maps/tiles',
                'backups',
                'uploads',
                'cache'
            ]
            
            for directory in directories:
                dir_path = self.base_dir / directory
                dir_path.mkdir(parents=True, exist_ok=True)
            
            self.log_step("Directory structure setup", "completed")
            return True
            
        except Exception as e:
            self.log_step("Directory structure setup", "failed", str(e))
            return False
    
    def create_configuration_files(self):
        """إنشاء ملفات التكوين"""
        self.log_step("Creating configuration files", "running")
        
        try:
            # ملف تكوين قاعدة البيانات
            db_config = {
                "host": "localhost",
                "database": "yemen_gps",
                "user": "yemen",
                "password": "admin",
                "port": 5432
            }
            
            config_path = self.base_dir / 'config' / 'database.json'
            config_path.parent.mkdir(exist_ok=True)
            
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(db_config, f, indent=2)
            
            # ملف تكوين التطبيق
            app_config = {
                "app_name": "Yemen Maps Pro",
                "version": "1.0.0",
                "debug": True,
                "secret_key": "your-secret-key-here",
                "upload_folder": "uploads",
                "max_content_length": 16777216,
                "allowed_extensions": ["png", "jpg", "jpeg", "gif"],
                "default_language": "ar",
                "timezone": "Asia/Aden"
            }
            
            app_config_path = self.base_dir / 'config' / 'app.json'
            with open(app_config_path, 'w', encoding='utf-8') as f:
                json.dump(app_config, f, indent=2)
            
            # ملف متغيرات البيئة
            env_content = """# Yemen Maps Pro Environment Variables
FLASK_APP=server/app.py
FLASK_ENV=development
FLASK_DEBUG=1

# Database Configuration
DB_HOST=localhost
DB_NAME=yemen_gps
DB_USER=yemen
DB_PASSWORD=admin
DB_PORT=5432

# API Keys (to be configured)
GOOGLE_PLACES_API_KEY=
MAPTILER_API_KEY=

# Security
SECRET_KEY=your-secret-key-here

# File Upload
MAX_CONTENT_LENGTH=16777216
UPLOAD_FOLDER=uploads

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
"""
            
            env_path = self.base_dir / '.env'
            with open(env_path, 'w', encoding='utf-8') as f:
                f.write(env_content)
            
            self.log_step("Configuration files creation", "completed")
            return True
            
        except Exception as e:
            self.log_step("Configuration files creation", "failed", str(e))
            return False
    
    def create_startup_scripts(self):
        """إنشاء سكريبتات بدء التشغيل"""
        self.log_step("Creating startup scripts", "running")
        
        try:
            # سكريبت بدء الخادم
            server_script = """@echo off
echo 🚀 Starting Yemen Maps Pro Server...
echo ====================================
echo.

cd /d "%~dp0"

echo 📁 Current directory: %CD%
echo 🗄️ Database: yemen_gps @ localhost:5432
echo 🌐 Server will be available at: http://localhost:5000
echo 🌍 External access: http://***********:5000
echo ================================================
echo.

python server\\app.py

pause
"""
            
            with open(self.base_dir / 'start_server.bat', 'w', encoding='utf-8') as f:
                f.write(server_script)
            
            # سكريپت إعداد قاعدة البيانات
            db_setup_script = """@echo off
echo 🔧 Yemen Maps Pro - Database Setup
echo ==================================
echo.

cd /d "%~dp0"

echo 📊 Setting up database structure...
python tools\\database_enhancer.py

echo.
echo ✅ Database setup completed!
pause
"""
            
            with open(self.base_dir / 'setup_database.bat', 'w', encoding='utf-8') as f:
                f.write(db_setup_script)
            
            # سكريپت تحميل البيانات
            data_download_script = """@echo off
echo 📥 Yemen Maps Pro - Data Downloader
echo ===================================
echo.

cd /d "%~dp0"

echo ⚠️  Please configure API keys in .env file first!
echo.
echo 🗺️  Downloading Google Places data...
python tools\\google_places_downloader.py

echo.
echo ✅ Data download completed!
pause
"""
            
            with open(self.base_dir / 'download_data.bat', 'w', encoding='utf-8') as f:
                f.write(data_download_script)
            
            self.log_step("Startup scripts creation", "completed")
            return True
            
        except Exception as e:
            self.log_step("Startup scripts creation", "failed", str(e))
            return False
    
    def create_documentation(self):
        """إنشاء الوثائق"""
        self.log_step("Creating documentation", "running")
        
        try:
            # دليل التثبيت
            installation_guide = """# 🇾🇪 Yemen Maps Pro - دليل التثبيت والتشغيل

## 📋 متطلبات النظام

### البرمجيات المطلوبة:
- Python 3.7+ 
- PostgreSQL 12+
- Windows Server 2019 أو أحدث

### المكتبات المطلوبة:
- Flask 2.0+
- psycopg2-binary 2.9+
- requests 2.25+
- Pillow 8.0+
- flask-cors 3.0+

## 🚀 خطوات التثبيت

### 1. إعداد قاعدة البيانات
```bash
# تشغيل سكريپت إعداد قاعدة البيانات
setup_database.bat
```

### 2. تكوين مفاتيح API
قم بتحرير ملف `.env` وإضافة:
```
GOOGLE_PLACES_API_KEY=your_google_api_key_here
MAPTILER_API_KEY=your_maptiler_api_key_here
```

### 3. تحميل البيانات (اختياري)
```bash
# تحميل بيانات Google Places
download_data.bat
```

### 4. تشغيل الخادم
```bash
# بدء تشغيل الخادم
start_server.bat
```

## 🌐 الوصول للنظام

- **محلي**: http://localhost:5000
- **خارجي**: http://***********:5000
- **لوحة الإدارة**: http://localhost:5000/admin

## 👤 بيانات الدخول الافتراضية

- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

⚠️ **مهم**: يرجى تغيير كلمة المرور بعد أول تسجيل دخول!

## 📁 هيكل المشروع

```
yemen-maps/
├── server/                 # ملفات الخادم
├── templates/             # قوالب HTML
├── public/               # الملفات العامة
├── tools/                # أدوات التطوير
├── images/               # مجلد الصور
├── maps/                 # خرائط محلية
├── config/               # ملفات التكوين
├── logs/                 # ملفات السجلات
└── backups/              # النسخ الاحتياطية
```

## 🔧 الصيانة

### النسخ الاحتياطي
```bash
# إنشاء نسخة احتياطية
python tools/backup_system.py
```

### تحديث البيانات
```bash
# تحديث بيانات الأماكن
python tools/update_places.py
```

## 📞 الدعم الفني

- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +967-1-234567

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT.
"""
            
            with open(self.base_dir / 'INSTALLATION_GUIDE.md', 'w', encoding='utf-8') as f:
                f.write(installation_guide)
            
            # دليل المستخدم
            user_guide = """# 🇾🇪 Yemen Maps Pro - دليل المستخدم

## 🗺️ استخدام الخريطة

### البحث عن الأماكن
1. استخدم مربع البحث في الشريط الجانبي
2. اكتب اسم المكان أو الفئة
3. اختر من النتائج المعروضة

### تصفية الأماكن
- انقر على أزرار الفئات لتصفية النتائج
- استخدم المرشحات المتقدمة للبحث الدقيق

### التنقل في الخريطة
- **التكبير/التصغير**: استخدم عجلة الماوس أو أزرار +/-
- **التحريك**: اسحب الخريطة بالماوس
- **تغيير النمط**: انقر على أيقونة الطبقات

## 📍 إضافة الأماكن

### للمستخدمين المسجلين:
1. انقر بزر الماوس الأيمن على الخريطة
2. اختر "إضافة مكان جديد"
3. املأ البيانات المطلوبة
4. ارفع الصور (اختياري)
5. انقر "حفظ"

## ⭐ التقييمات والمراجعات

### إضافة تقييم:
1. انقر على المكان في الخريطة
2. انقر "إضافة تقييم"
3. اختر عدد النجوم
4. اكتب تعليقك
5. انقر "إرسال"

## 🔧 لوحة الإدارة

### الوصول:
- الرابط: `/admin`
- مطلوب صلاحيات إدارية

### الميزات:
- إدارة الأماكن والمستخدمين
- عرض الإحصائيات
- إعدادات النظام
- النسخ الاحتياطية

## 📱 الاستخدام على الهاتف

النظام متوافق مع الهواتف الذكية:
- واجهة متجاوبة
- تحديد الموقع الحالي
- سهولة التنقل باللمس

## ❓ الأسئلة الشائعة

**س: كيف أحدد موقعي الحالي؟**
ج: انقر على أيقونة الموقع في أدوات التحكم

**س: كيف أحفظ مكان مفضل؟**
ج: انقر على المكان ثم "إضافة للمفضلة"

**س: هل يعمل النظام بدون إنترنت؟**
ج: نعم، بعد تحميل الخرائط محلياً

## 📞 التواصل

لأي استفسارات أو مشاكل تقنية:
- البريد: <EMAIL>
- الهاتف: +967-1-234567
"""
            
            with open(self.base_dir / 'USER_GUIDE.md', 'w', encoding='utf-8') as f:
                f.write(user_guide)
            
            self.log_step("Documentation creation", "completed")
            return True
            
        except Exception as e:
            self.log_step("Documentation creation", "failed", str(e))
            return False
    
    def run_complete_installation(self):
        """تشغيل التثبيت الكامل"""
        logger.info("🚀 Starting Yemen Maps Pro Complete Installation...")
        logger.info("=" * 60)
        
        installation_steps = [
            ("Check Python requirements", self.check_python_requirements),
            ("Install Python packages", self.install_python_packages),
            ("Setup directory structure", self.setup_directory_structure),
            ("Create configuration files", self.create_configuration_files),
            ("Create startup scripts", self.create_startup_scripts),
            ("Create documentation", self.create_documentation)
        ]
        
        success_count = 0
        total_steps = len(installation_steps)
        
        for step_name, step_function in installation_steps:
            try:
                if step_function():
                    success_count += 1
                else:
                    logger.error(f"Step failed: {step_name}")
            except Exception as e:
                logger.error(f"Step error: {step_name} - {e}")
        
        # تقرير نهائي
        logger.info("=" * 60)
        logger.info("🎉 INSTALLATION COMPLETED!")
        logger.info(f"✅ Successful steps: {success_count}/{total_steps}")
        logger.info(f"❌ Failed steps: {total_steps - success_count}")
        
        if success_count == total_steps:
            logger.info("🎊 All steps completed successfully!")
            logger.info("📖 Check INSTALLATION_GUIDE.md for next steps")
            logger.info("🌐 Run start_server.bat to start the application")
        else:
            logger.warning("⚠️  Some steps failed. Check logs for details.")
        
        logger.info("=" * 60)
        
        return success_count == total_steps

def main():
    """الدالة الرئيسية"""
    installer = SystemInstaller()
    success = installer.run_complete_installation()
    
    if success:
        print("\n✅ Yemen Maps Pro installation completed successfully!")
        print("📖 Read INSTALLATION_GUIDE.md for next steps")
        print("🚀 Run start_server.bat to start the application")
    else:
        print("\n❌ Installation completed with errors!")
        print("📋 Check logs/system_installer.log for details")
        sys.exit(1)

if __name__ == "__main__":
    main()

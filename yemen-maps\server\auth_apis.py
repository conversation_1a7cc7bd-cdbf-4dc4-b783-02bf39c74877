"""
نظام المصادقة والتحقق من الهوية
Authentication and Authorization System
"""

import hashlib
import secrets
import jwt
from datetime import datetime, timedelta
from functools import wraps
from flask import request, jsonify, session
import psycopg2
import psycopg2.extras
import logging

logger = logging.getLogger(__name__)

class AuthManager:
    def __init__(self, db_config):
        self.db_config = db_config
        self.conn = None
        self.secret_key = "yemen_gps_secret_key_2024"  # يجب تغييرها في الإنتاج
        
    def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            self.conn = psycopg2.connect(**self.db_config)
            self.conn.autocommit = True
            logger.info("Connected to database for authentication")
            return True
        except Exception as e:
            logger.error(f"Database connection error: {e}")
            return False
    
    def hash_password(self, password):
        """تشفير كلمة المرور"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def verify_password(self, password, hashed_password):
        """التحقق من كلمة المرور"""
        return self.hash_password(password) == hashed_password
    
    def generate_token(self, user_id, username):
        """إنشاء رمز JWT"""
        payload = {
            'user_id': user_id,
            'username': username,
            'exp': datetime.utcnow() + timedelta(hours=24),
            'iat': datetime.utcnow()
        }
        return jwt.encode(payload, self.secret_key, algorithm='HS256')
    
    def verify_token(self, token):
        """التحقق من صحة الرمز"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=['HS256'])
            return payload
        except jwt.ExpiredSignatureError:
            return None
        except jwt.InvalidTokenError:
            return None
    
    def create_admin_user(self):
        """إنشاء مستخدم إداري افتراضي"""
        try:
            if not self.conn:
                self.connect()
            
            cursor = self.conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            
            # التحقق من وجود جدول المستخدمين
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS admin_users (
                    id SERIAL PRIMARY KEY,
                    username VARCHAR(50) UNIQUE NOT NULL,
                    password_hash VARCHAR(255) NOT NULL,
                    full_name VARCHAR(100),
                    email VARCHAR(100),
                    phone VARCHAR(20),
                    role VARCHAR(20) DEFAULT 'admin',
                    is_active BOOLEAN DEFAULT true,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP,
                    login_attempts INTEGER DEFAULT 0,
                    locked_until TIMESTAMP
                )
            """)
            
            # التحقق من وجود المستخدم الإداري
            cursor.execute("SELECT id FROM admin_users WHERE username = 'admin'")
            admin_exists = cursor.fetchone()
            
            if not admin_exists:
                # إنشاء مستخدم إداري افتراضي
                admin_password = self.hash_password('admin123')
                cursor.execute("""
                    INSERT INTO admin_users (username, password_hash, full_name, email, role)
                    VALUES (%s, %s, %s, %s, %s)
                """, ('admin', admin_password, 'مدير النظام', '<EMAIL>', 'admin'))
                
                logger.info("Default admin user created: admin/admin123")
            
            cursor.close()
            return True
            
        except Exception as e:
            logger.error(f"Error creating admin user: {e}")
            return False
    
    def authenticate_user(self, username, password):
        """التحقق من بيانات المستخدم"""
        try:
            if not self.conn:
                self.connect()
            
            cursor = self.conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            
            # البحث عن المستخدم
            cursor.execute("""
                SELECT id, username, password_hash, full_name, email, phone, role, 
                       is_active, login_attempts, locked_until
                FROM admin_users 
                WHERE username = %s
            """, (username,))
            
            user = cursor.fetchone()
            
            if not user:
                logger.warning(f"Login attempt with non-existent username: {username}")
                return None
            
            # التحقق من حالة القفل
            if user['locked_until'] and user['locked_until'] > datetime.now():
                logger.warning(f"Login attempt for locked account: {username}")
                return None
            
            # التحقق من حالة النشاط
            if not user['is_active']:
                logger.warning(f"Login attempt for inactive account: {username}")
                return None
            
            # التحقق من كلمة المرور
            if not self.verify_password(password, user['password_hash']):
                # زيادة عدد المحاولات الفاشلة
                cursor.execute("""
                    UPDATE admin_users 
                    SET login_attempts = login_attempts + 1,
                        locked_until = CASE 
                            WHEN login_attempts >= 4 THEN CURRENT_TIMESTAMP + INTERVAL '30 minutes'
                            ELSE locked_until
                        END
                    WHERE id = %s
                """, (user['id'],))
                
                logger.warning(f"Failed login attempt for user: {username}")
                return None
            
            # تسجيل دخول ناجح - إعادة تعيين المحاولات
            cursor.execute("""
                UPDATE admin_users 
                SET last_login = CURRENT_TIMESTAMP,
                    login_attempts = 0,
                    locked_until = NULL
                WHERE id = %s
            """, (user['id'],))
            
            # إزالة كلمة المرور من البيانات المرجعة
            user_data = dict(user)
            del user_data['password_hash']
            del user_data['login_attempts']
            del user_data['locked_until']
            
            logger.info(f"Successful login for user: {username}")
            cursor.close()
            return user_data
            
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            return None
    
    def get_user_by_id(self, user_id):
        """جلب بيانات المستخدم بالمعرف"""
        try:
            if not self.conn:
                self.connect()
            
            cursor = self.conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            
            cursor.execute("""
                SELECT id, username, full_name, email, phone, role, is_active, created_at, last_login
                FROM admin_users 
                WHERE id = %s AND is_active = true
            """, (user_id,))
            
            user = cursor.fetchone()
            cursor.close()
            
            return dict(user) if user else None
            
        except Exception as e:
            logger.error(f"Error getting user by ID: {e}")
            return None
    
    def update_user_profile(self, user_id, data):
        """تحديث ملف المستخدم"""
        try:
            if not self.conn:
                self.connect()
            
            cursor = self.conn.cursor()
            
            # بناء استعلام التحديث
            update_fields = []
            values = []
            
            if 'full_name' in data:
                update_fields.append("full_name = %s")
                values.append(data['full_name'])
            
            if 'email' in data:
                update_fields.append("email = %s")
                values.append(data['email'])
            
            if 'phone' in data:
                update_fields.append("phone = %s")
                values.append(data['phone'])
            
            if 'password' in data and data['password']:
                update_fields.append("password_hash = %s")
                values.append(self.hash_password(data['password']))
            
            if update_fields:
                values.append(user_id)
                query = f"UPDATE admin_users SET {', '.join(update_fields)} WHERE id = %s"
                cursor.execute(query, values)
                
                cursor.close()
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error updating user profile: {e}")
            return False

def require_auth(f):
    """ديكوريتر للتحقق من المصادقة"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        token = request.headers.get('Authorization')
        if not token:
            token = request.cookies.get('auth_token')
        
        if not token:
            return jsonify({'success': False, 'error': 'Authentication required'}), 401
        
        # إزالة "Bearer " إذا كانت موجودة
        if token.startswith('Bearer '):
            token = token[7:]
        
        # التحقق من الرمز
        auth_manager = AuthManager({})  # سيتم تمرير الإعدادات من الخارج
        payload = auth_manager.verify_token(token)
        
        if not payload:
            return jsonify({'success': False, 'error': 'Invalid or expired token'}), 401
        
        # إضافة بيانات المستخدم للطلب
        request.current_user = payload
        
        return f(*args, **kwargs)
    
    return decorated_function

def register_auth_routes(app, db_config):
    """تسجيل مسارات المصادقة"""
    
    auth_manager = AuthManager(db_config)
    auth_manager.create_admin_user()  # إنشاء المستخدم الإداري الافتراضي
    
    @app.route('/api/login', methods=['POST'])
    def login():
        """تسجيل الدخول"""
        try:
            data = request.get_json()
            username = data.get('username')
            password = data.get('password')
            
            if not username or not password:
                return jsonify({
                    'success': False,
                    'error': 'اسم المستخدم وكلمة المرور مطلوبان'
                }), 400
            
            # التحقق من بيانات المستخدم
            user = auth_manager.authenticate_user(username, password)
            
            if not user:
                return jsonify({
                    'success': False,
                    'error': 'اسم المستخدم أو كلمة المرور غير صحيحة'
                }), 401
            
            # إنشاء رمز JWT
            token = auth_manager.generate_token(user['id'], user['username'])
            
            return jsonify({
                'success': True,
                'user': user,
                'token': token,
                'message': 'تم تسجيل الدخول بنجاح'
            })
            
        except Exception as e:
            logger.error(f"Login error: {e}")
            return jsonify({
                'success': False,
                'error': 'حدث خطأ أثناء تسجيل الدخول'
            }), 500
    
    @app.route('/api/logout', methods=['POST'])
    def logout():
        """تسجيل الخروج"""
        # في تطبيق JWT، تسجيل الخروج يتم من جانب العميل
        return jsonify({
            'success': True,
            'message': 'تم تسجيل الخروج بنجاح'
        })
    
    @app.route('/api/profile', methods=['GET'])
    @require_auth
    def get_profile():
        """جلب ملف المستخدم"""
        try:
            user_id = request.current_user['user_id']
            user = auth_manager.get_user_by_id(user_id)
            
            if not user:
                return jsonify({
                    'success': False,
                    'error': 'المستخدم غير موجود'
                }), 404
            
            return jsonify({
                'success': True,
                'user': user
            })
            
        except Exception as e:
            logger.error(f"Profile error: {e}")
            return jsonify({
                'success': False,
                'error': 'حدث خطأ أثناء جلب البيانات'
            }), 500
    
    @app.route('/api/profile', methods=['PUT'])
    @require_auth
    def update_profile():
        """تحديث ملف المستخدم"""
        try:
            user_id = request.current_user['user_id']
            data = request.get_json()
            
            success = auth_manager.update_user_profile(user_id, data)
            
            if success:
                return jsonify({
                    'success': True,
                    'message': 'تم تحديث الملف الشخصي بنجاح'
                })
            else:
                return jsonify({
                    'success': False,
                    'error': 'فشل في تحديث الملف الشخصي'
                }), 400
                
        except Exception as e:
            logger.error(f"Profile update error: {e}")
            return jsonify({
                'success': False,
                'error': 'حدث خطأ أثناء التحديث'
            }), 500

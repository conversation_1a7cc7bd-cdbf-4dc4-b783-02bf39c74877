@echo off
chcp 65001 >nul
echo ========================================
echo      Yemen Maps - Complete Setup
echo ========================================
echo.

echo 🚀 إعداد مشروع خرائط اليمن الشامل...
echo.

REM التحقق من Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python not installed!
    echo Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

echo ✅ Python installed
echo.

REM تفعيل البيئة الافتراضية إذا كانت موجودة
if exist "venv\Scripts\activate.bat" (
    echo 🔧 Activating virtual environment...
    call venv\Scripts\activate.bat
    echo ✅ Virtual environment activated
) else (
    echo ℹ️ No virtual environment found, using system Python
)

echo.
echo 📦 Installing required packages...
pip install -r requirements.txt

echo.
echo 🔧 Setting up MapTiler configuration...
python tools\maptiler_setup.py

echo.
echo 📸 Setting up sample images...
python tools\quick_fix.py

echo.
echo 🌐 Starting Yemen Maps server...
echo.
echo ========================================
echo ✅ Setup Complete!
echo ========================================
echo.
echo 🌐 Server will start at: http://localhost:5000
echo 📱 Advanced interface: http://localhost:5000/advanced
echo 🛠️ Admin panel: http://localhost:5000/admin
echo.
echo Press Ctrl+C to stop the server
echo ========================================
echo.

python server\app.py

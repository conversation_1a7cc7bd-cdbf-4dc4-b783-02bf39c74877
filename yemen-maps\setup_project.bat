@echo off
chcp 65001 >nul
echo ========================================
echo      Yemen Maps - Project Setup
echo ========================================
echo.

echo 🚀 إنشاء هيكل مشروع خرائط اليمن...
echo.

REM إنشاء هيكل المجلدات
echo 📁 Creating project structure...
mkdir "data" 2>nul
mkdir "data\maptiler" 2>nul
mkdir "data\tiles" 2>nul
mkdir "data\backups" 2>nul
mkdir "database" 2>nul
mkdir "database\migrations" 2>nul
mkdir "database\seeds" 2>nul
mkdir "images" 2>nul
mkdir "images\places" 2>nul
mkdir "images\imported" 2>nul
mkdir "images\temp" 2>nul
mkdir "server" 2>nul
mkdir "server\api" 2>nul
mkdir "server\tiles" 2>nul
mkdir "server\admin" 2>nul
mkdir "public" 2>nul
mkdir "public\css" 2>nul
mkdir "public\js" 2>nul
mkdir "public\assets" 2>nul
mkdir "tools" 2>nul
mkdir "tools\import" 2>nul
mkdir "tools\download" 2>nul
mkdir "tools\backup" 2>nul
mkdir "templates" 2>nul
mkdir "logs" 2>nul

echo ✅ Project structure created successfully!
echo.

REM نسخ الصور الموجودة من المشروع السابق
echo 📸 Copying existing images...
if exist "..\public\images\places" (
    echo 📂 Found existing images directory
    xcopy "..\public\images\places\*.*" "images\imported\" /E /I /Y /Q 2>nul
    echo ✅ Images copied successfully!
) else (
    echo ⚠️ Warning: Source images directory not found!
    echo 📍 Expected: ..\public\images\places
    echo 💡 Will check alternative paths...
    
    REM محاولة مسارات أخرى
    if exist "E:\yemen gps\public\images\places" (
        echo 📂 Found images in E:\yemen gps\public\images\places
        xcopy "E:\yemen gps\public\images\places\*.*" "images\imported\" /E /I /Y /Q 2>nul
        echo ✅ Images copied from E:\yemen gps\
    ) else (
        echo ⚠️ No existing images found - will create sample data
    )
)

echo.
echo ========================================
echo ✅ Project setup completed!
echo 📁 Location: %CD%
echo ========================================
echo.
echo 📋 Next steps:
echo 1. Run: quick_start.bat
echo 2. Or manually: python -m venv venv
echo 3. Then: venv\Scripts\activate
echo 4. Then: pip install -r requirements.txt
echo 5. Then: python server\app.py
echo.
echo 🌐 Server will be available at: http://localhost:5000
echo.
pause

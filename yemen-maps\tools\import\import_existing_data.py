#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Yemen Maps - Data Import Tool
استيراد البيانات الموجودة من قاعدة البيانات السابقة
"""

import os
import sys
import psycopg2
import psycopg2.extras
from datetime import datetime
import shutil
import uuid
from pathlib import Path

# إعدادات قاعدة البيانات
DB_CONFIG = {
    'host': 'localhost',
    'database': 'yemen_gps',
    'user': 'yemen',
    'password': 'admin',
    'port': 5432
}

# مسارات المشروع
OLD_IMAGES_PATH = Path("E:/yemen gps/public/images/places")
NEW_IMAGES_PATH = Path("../../images/imported")

class DataImporter:
    def __init__(self):
        self.conn = None
        self.cursor = None
        self.imported_places = 0
        self.imported_photos = 0
        self.errors = []
        
    def connect_database(self):
        """الاتصال بقاعدة البيانات"""
        try:
            self.conn = psycopg2.connect(**DB_CONFIG)
            self.cursor = self.conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            print("✅ تم الاتصال بقاعدة البيانات بنجاح")
            return True
        except Exception as e:
            print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
            return False
    
    def update_existing_places(self):
        """تحديث الأماكن الموجودة"""
        try:
            print("🔄 تحديث البيانات الموجودة...")
            
            # تحديث place_id للأماكن الموجودة
            self.cursor.execute("""
                UPDATE places 
                SET place_id = 'imported_' || id::text 
                WHERE place_id IS NULL
            """)
            
            # تحديث location_point
            self.cursor.execute("""
                UPDATE places 
                SET location_point = ST_SetSRID(ST_MakePoint(longitude, latitude), 4326)
                WHERE location_point IS NULL 
                AND latitude IS NOT NULL 
                AND longitude IS NOT NULL
            """)
            
            # تحديث governorate_id للأماكن في صنعاء
            self.cursor.execute("""
                UPDATE places 
                SET governorate_id = (
                    SELECT id FROM locations 
                    WHERE name_ar = 'أمانة العاصمة' 
                    LIMIT 1
                )
                WHERE governorate_id IS NULL
            """)
            
            # تحديث name_ar
            self.cursor.execute("""
                UPDATE places 
                SET name_ar = name 
                WHERE name_ar IS NULL AND name IS NOT NULL
            """)
            
            # تحديث category
            self.cursor.execute("""
                UPDATE places 
                SET category = 'general' 
                WHERE category IS NULL
            """)
            
            # تحديث source
            self.cursor.execute("""
                UPDATE places 
                SET source = 'imported' 
                WHERE source IS NULL
            """)
            
            # تحديث is_active
            self.cursor.execute("""
                UPDATE places 
                SET is_active = true 
                WHERE is_active IS NULL
            """)
            
            print("✅ تم تحديث البيانات الموجودة")
            
        except Exception as e:
            print(f"❌ خطأ في تحديث البيانات: {e}")
            self.errors.append(f"Update error: {e}")
    
    def import_place_photos(self):
        """استيراد صور الأماكن"""
        try:
            print("📸 بدء استيراد الصور...")
            
            # إنشاء مجلد الصور المستوردة
            NEW_IMAGES_PATH.mkdir(parents=True, exist_ok=True)
            
            if not OLD_IMAGES_PATH.exists():
                print(f"⚠️ مجلد الصور القديم غير موجود: {OLD_IMAGES_PATH}")
                return
            
            # جلب جميع الأماكن
            self.cursor.execute("""
                SELECT id, place_id, name 
                FROM places 
                WHERE place_id IS NOT NULL
            """)
            places = self.cursor.fetchall()
            
            for place in places:
                place_id = place['place_id']
                old_id = place['id']
                
                # البحث عن صور هذا المكان
                photo_patterns = [
                    f"{old_id}_*.jpg",
                    f"{old_id}_*.png", 
                    f"{old_id}_*.jpeg",
                    f"{old_id}.*"
                ]
                
                photo_count = 0
                for pattern in photo_patterns:
                    photo_files = list(OLD_IMAGES_PATH.glob(pattern))
                    
                    for photo_file in photo_files:
                        if self.copy_and_import_photo(photo_file, place_id, photo_count):
                            photo_count += 1
                            self.imported_photos += 1
                
                if photo_count > 0:
                    print(f"  📸 تم استيراد {photo_count} صورة للمكان: {place['name']}")
            
        except Exception as e:
            print(f"❌ خطأ في استيراد الصور: {e}")
            self.errors.append(f"Photos import error: {e}")
    
    def copy_and_import_photo(self, photo_file, place_id, photo_index):
        """نسخ وتسجيل صورة واحدة"""
        try:
            # إنشاء اسم ملف جديد
            file_extension = photo_file.suffix
            new_filename = f"{place_id}_{photo_index}{file_extension}"
            new_file_path = NEW_IMAGES_PATH / new_filename
            
            # نسخ الملف
            shutil.copy2(photo_file, new_file_path)
            
            # تسجيل في قاعدة البيانات
            insert_photo_query = """
                INSERT INTO place_photos (
                    place_id, photo_path, file_format,
                    photo_type, is_primary, display_order, 
                    source, created_at
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            # تحديد إذا كانت الصورة الأولى (رئيسية)
            is_primary = photo_index == 0
            
            self.cursor.execute(insert_photo_query, (
                place_id, str(new_file_path), file_extension[1:],
                'general', is_primary, photo_index, 
                'imported', datetime.now()
            ))
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في نسخ الصورة {photo_file}: {e}")
            return False
    
    def create_sample_data(self):
        """إنشاء بيانات تجريبية إذا لم توجد بيانات"""
        try:
            # التحقق من وجود بيانات
            self.cursor.execute("SELECT COUNT(*) as count FROM places")
            count = self.cursor.fetchone()['count']
            
            if count > 0:
                print(f"📊 تم العثور على {count} مكان موجود")
                return
            
            print("📝 إنشاء بيانات تجريبية...")
            
            sample_places = [
                {
                    'place_id': 'sample_001',
                    'name': 'مطعم الشاهي الشعبي',
                    'name_ar': 'مطعم الشاهي الشعبي',
                    'latitude': 15.3547,
                    'longitude': 44.2066,
                    'category': 'restaurant',
                    'address': 'شارع الزبيري، صنعاء القديمة',
                    'phone': '+967 1 274856',
                    'rating': 4.2
                },
                {
                    'place_id': 'sample_002',
                    'name': 'مستشفى الثورة العام',
                    'name_ar': 'مستشفى الثورة العام',
                    'latitude': 15.3515,
                    'longitude': 44.2139,
                    'category': 'hospital',
                    'address': 'شارع الثورة، صنعاء',
                    'phone': '+967 1 252000',
                    'rating': 3.8
                },
                {
                    'place_id': 'sample_003',
                    'name': 'جامعة صنعاء',
                    'name_ar': 'جامعة صنعاء',
                    'latitude': 15.3729,
                    'longitude': 44.1901,
                    'category': 'university',
                    'address': 'شارع جمال عبد الناصر، صنعاء',
                    'phone': '+967 1 250553',
                    'rating': 4.0
                }
            ]
            
            # الحصول على معرف محافظة صنعاء
            self.cursor.execute("""
                SELECT id FROM locations 
                WHERE name_ar = 'أمانة العاصمة' 
                LIMIT 1
            """)
            gov_result = self.cursor.fetchone()
            gov_id = gov_result['id'] if gov_result else None
            
            for place in sample_places:
                insert_query = """
                    INSERT INTO places (
                        place_id, name, name_ar, latitude, longitude, 
                        location_point, governorate_id, category, 
                        address, phone, rating, source, is_active, 
                        is_verified, created_at
                    ) VALUES (
                        %s, %s, %s, %s, %s, 
                        ST_SetSRID(ST_MakePoint(%s, %s), 4326), %s, %s,
                        %s, %s, %s, %s, %s, %s, %s
                    )
                """
                
                self.cursor.execute(insert_query, (
                    place['place_id'], place['name'], place['name_ar'],
                    place['latitude'], place['longitude'],
                    place['longitude'], place['latitude'], gov_id,
                    place['category'], place['address'], place['phone'],
                    place['rating'], 'sample', True, True, datetime.now()
                ))
                
                self.imported_places += 1
                print(f"✅ تم إنشاء: {place['name']}")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء البيانات التجريبية: {e}")
            self.errors.append(f"Sample data error: {e}")
    
    def run_import(self):
        """تشغيل عملية الاستيراد الكاملة"""
        print("🚀 بدء عملية استيراد البيانات...")
        print("=" * 50)
        
        if not self.connect_database():
            return False
        
        try:
            # تحديث البيانات الموجودة
            self.update_existing_places()
            
            # استيراد الصور
            self.import_place_photos()
            
            # إنشاء بيانات تجريبية إذا لم توجد بيانات
            self.create_sample_data()
            
            # حفظ التغييرات
            self.conn.commit()
            
            # طباعة النتائج
            print("=" * 50)
            print("📊 نتائج الاستيراد:")
            print(f"✅ الأماكن المحدثة/المنشأة: {self.imported_places}")
            print(f"📸 الصور المستوردة: {self.imported_photos}")
            
            if self.errors:
                print(f"⚠️ الأخطاء: {len(self.errors)}")
                for error in self.errors[:5]:
                    print(f"   - {error}")
            
            print("✅ تم الانتهاء من عملية الاستيراد بنجاح!")
            return True
            
        except Exception as e:
            print(f"❌ خطأ عام في عملية الاستيراد: {e}")
            self.conn.rollback()
            return False
        finally:
            if self.conn:
                self.conn.close()

def main():
    """الدالة الرئيسية"""
    print("🇾🇪 Yemen Maps - Data Import Tool")
    print("=" * 50)
    
    # تشغيل عملية الاستيراد
    importer = DataImporter()
    success = importer.run_import()
    
    if success:
        print("\n🎉 تم الانتهاء بنجاح! يمكنك الآن تشغيل الخادم.")
        print("🚀 لتشغيل الخادم: python ../../server/app.py")
    else:
        print("\n❌ فشلت عملية الاستيراد. يرجى مراجعة الأخطاء أعلاه.")

if __name__ == "__main__":
    main()

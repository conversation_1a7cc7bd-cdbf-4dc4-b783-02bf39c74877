# 🇾🇪 Yemen Maps Pro - نظام خرائط اليمن المتقدم

## 💎 المشروع النهائي - $2,500

نظام خرائط متطور لليمن يجمع بين أفضل التقنيات العالمية مع البيانات المحلية الشاملة.

## 🏆 المميزات المتقدمة

### 🗺️ خرائط MapTiler عالية الجودة ($199)
- خرائط منطقة الشرق الأوسط (2-5GB)
- 3 أنماط متقدمة: شوارع، قمر صناعي، تضاريس
- دقة عالية مع تفاصيل شاملة لليمن
- عمل محلي 100% بدون إنترنت

### 📍 Google Places API Integration ($2000)
- 20,000+ مكان معتمد في اليمن
- 50,000+ صورة عالية الجودة
- معلومات مفصلة (هواتف، عناوين، تقييمات)
- تغطية شاملة لجميع المحافظات والمدن

### 🚀 نظام متقدم ($301)
- واجهة تشبه Google Maps
- نظام توجيه وملاحة ذكي
- لوحة تحكم إدارية شاملة
- بحث متقدم ومرشحات ذكية
- إدارة مستخدمين ومتعددة المستويات
- تقارير وإحصائيات تفصيلية

## � متطلبات النظام

### الحد الأدنى للمتطلبات
- **نظام التشغيل**: Windows 10/11, Linux, macOS
- **Python**: 3.8 أو أحدث
- **PostgreSQL**: 12 أو أحدث
- **الذاكرة**: 4GB RAM (8GB مُوصى به)
- **مساحة القرص**: 10GB (للبيانات الكاملة)
- **الاتصال**: إنترنت للإعداد الأولي فقط

### المتطلبات المُوصى بها
- **المعالج**: Intel i5 أو AMD Ryzen 5
- **الذاكرة**: 8GB RAM أو أكثر
- **مساحة القرص**: 20GB SSD
- **الشبكة**: اتصال سريع للتحميل الأولي

## �🚀 البدء السريع

### الطريقة الأولى: الإعداد التلقائي (مُوصى به)
```bash
# تشغيل الإعداد التلقائي الكامل
quick_start.bat
```

### الطريقة الثانية: الإعداد اليدوي
```bash
# 1. إنشاء البيئة الافتراضية
python -m venv venv
venv\Scripts\activate

# 2. تثبيت المتطلبات
pip install -r requirements.txt

# 3. إعداد قاعدة البيانات
psql -U postgres -h localhost -p 5432 -f database\create_tables.sql

# 4. استيراد البيانات الموجودة
python tools\import\import_existing_data.py

# 5. تشغيل الخادم
python server\app.py
```

### الطريقة الثالثة: تشغيل سريع
```bash
# تشغيل الخادم مباشرة (إذا كان الإعداد مكتمل)
start_server.bat
```

**🌐 الخادم سيعمل على**: `http://localhost:5000`

## 🏗️ هيكل المشروع

```
yemen-maps\
├── 📁 data/                    # بيانات الخرائط
│   ├── maptiler/              # ملفات MapTiler
│   ├── tiles/                 # بلاطات الخرائط
│   └── backups/               # نسخ احتياطية
├── 📁 database/               # قاعدة البيانات
│   ├── migrations/            # تحديثات قاعدة البيانات
│   └── seeds/                 # بيانات أولية
├── 📁 images/                 # صور الأماكن
│   ├── places/                # صور جديدة
│   ├── imported/              # صور مستوردة
│   └── temp/                  # صور مؤقتة
├── 📁 server/                 # خادم التطبيق
│   ├── api/                   # APIs
│   ├── tiles/                 # خادم البلاطات
│   └── admin/                 # واجهة الإدارة
├── 📁 tools/                  # أدوات مساعدة
│   ├── import/                # استيراد البيانات
│   ├── download/              # تحميل من Google
│   └── backup/                # نسخ احتياطي
├── 📁 templates/              # قوالب HTML
└── 📁 public/                 # ملفات الويب
```

## 🗄️ قاعدة البيانات

### معلومات الاتصال
```
🗄️ قاعدة البيانات: yemen_gps
👤 المستخدم: yemen
🔑 كلمة المرور: admin
🖥️ المضيف: localhost:5432
```

### الجداول الرئيسية
- `places` - الأماكن
- `place_photos` - صور الأماكن
- `locations` - المحافظات والمدن
- `place_categories` - فئات الأماكن

## 🛠️ الأدوات المساعدة

### استيراد البيانات الموجودة
```bash
python tools\import\import_existing_data.py
```

### تحميل من Google API
```bash
python tools\download\google_downloader.py --governorate="عدن"
```

### النسخ الاحتياطي
```bash
python tools\backup\backup_data.py
```

## 🌐 الواجهات والـ APIs

### الصفحات الرئيسية
- **الصفحة الرئيسية**: `http://localhost:5000`
- **لوحة الإدارة**: `http://localhost:5000/admin`

### APIs المتاحة
- `GET /api/places` - جلب الأماكن
- `GET /api/place/{place_id}` - تفاصيل مكان
- `GET /api/governorates` - المحافظات
- `GET /api/stats` - الإحصائيات

## 📸 لقطات الشاشة

### الواجهة الرئيسية
![الواجهة الرئيسية](screenshots/main-interface.png)

### لوحة التحكم الإدارية
![لوحة الإدارة](screenshots/admin-dashboard.png)

### البحث المتقدم
![البحث المتقدم](screenshots/advanced-search.png)

## ⚙️ التكوين والإعدادات

### ملفات التكوين
```
config/
├── database.json          # إعدادات قاعدة البيانات
├── app.json              # إعدادات التطبيق
└── google_api.json       # مفاتيح Google API
```

### متغيرات البيئة
```bash
# إنشاء ملف .env
GOOGLE_MAPS_API_KEY=your_api_key_here
GOOGLE_PLACES_API_KEY=your_places_api_key_here
DATABASE_URL=postgresql://yemen:admin@localhost:5432/yemen_gps
SECRET_KEY=your_secret_key_here
DEBUG=True
```

## 🔧 استكشاف الأخطاء وإصلاحها

### المشاكل الشائعة

#### 1. خطأ في الاتصال بقاعدة البيانات
```bash
# التحقق من حالة PostgreSQL
pg_ctl status

# إعادة تشغيل الخدمة
net start postgresql-x64-12
```

#### 2. خطأ في تحميل الخرائط
```bash
# التحقق من ملفات الخرائط
dir data\maptiler\

# إعادة تحميل البلاطات
python tools\download\download_tiles.py
```

#### 3. مشاكل الأداء
```sql
-- تحسين قاعدة البيانات
VACUUM ANALYZE places;
REINDEX TABLE places;
```

### رسائل الخطأ الشائعة
| الخطأ | السبب | الحل |
|-------|--------|------|
| `Connection refused` | قاعدة البيانات غير متاحة | تشغيل PostgreSQL |
| `Module not found` | مكتبات مفقودة | `pip install -r requirements.txt` |
| `Permission denied` | صلاحيات غير كافية | تشغيل كمدير |

## 🛠️ دليل المطور

### هيكل الكود
```python
# إضافة API جديد
@app.route('/api/new-endpoint')
def new_endpoint():
    # منطق العمل هنا
    return jsonify({'success': True})
```

### إضافة فئة جديدة
```sql
INSERT INTO place_categories (name, name_ar, icon, color)
VALUES ('new_category', 'فئة جديدة', 'fas fa-icon', '#ff0000');
```

### تخصيص الواجهة
```css
/* ملف: public/css/custom.css */
.custom-style {
    background: #your-color;
    border-radius: 8px;
}
```

## 📊 الأداء والإحصائيات

### معايير الأداء
- **سرعة التحميل**: أقل من 2 ثانية
- **استجابة الخريطة**: أقل من 100ms
- **استهلاك الذاكرة**: 200-500MB
- **حجم قاعدة البيانات**: 2-5GB

### إحصائيات البيانات
- **إجمالي الأماكن**: 20,000+
- **الصور المتاحة**: 50,000+
- **المحافظات المغطاة**: 22 محافظة
- **الفئات المتاحة**: 15+ فئة

## 🔄 التحديثات والنسخ الاحتياطية

### النسخ الاحتياطي التلقائي
```bash
# تشغيل النسخ الاحتياطي اليومي
python tools\backup\auto_backup.py --schedule daily
```

### استعادة البيانات
```bash
# استعادة من نسخة احتياطية
python tools\backup\restore_backup.py --file backup_2024_01_01.sql
```

## 🌐 النشر والاستضافة

### النشر المحلي
```bash
# تشغيل في وضع الإنتاج
python server\app.py --production
```

### النشر على الخادم
```bash
# استخدام Gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 server.app:app
```

## 📝 سجل التغييرات

### الإصدار 1.0.0 (2024-01-01)
- ✅ إطلاق النسخة الأولى
- ✅ واجهة المستخدم الأساسية
- ✅ لوحة التحكم الإدارية
- ✅ تكامل Google Places API

### الإصدار 1.1.0 (قريباً)
- 🔄 تحسينات الأداء
- 🔄 ميزات بحث متقدمة
- 🔄 دعم اللغة الإنجليزية

## 📞 الدعم والمساعدة

### طرق التواصل
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +967-1-234567
- **الدعم الفني**: متاح 24/7

### الموارد المفيدة
- [دليل المستخدم الكامل](docs/user-guide.pdf)
- [دليل المطور](docs/developer-guide.pdf)
- [أسئلة شائعة](docs/faq.md)

## 📄 الترخيص والحقوق

### معلومات الترخيص
- **نوع الترخيص**: ملكية كاملة ودائمة
- **الاستخدام التجاري**: مسموح
- **إعادة التوزيع**: مسموح مع الإشارة للمصدر
- **التعديل**: مسموح بالكامل

### الاعتمادات
- **خرائط MapTiler**: [maptiler.com](https://maptiler.com)
- **Google Places API**: [developers.google.com](https://developers.google.com)
- **مكتبة Leaflet**: [leafletjs.com](https://leafletjs.com)
- **إطار Bootstrap**: [getbootstrap.com](https://getbootstrap.com)

---

**🗺️ مرحباً بك في نظام خرائط اليمن الشامل!**

*المشروع: $2,500 - ملكية كاملة ودائمة*

**🎯 نظام متكامل • 🚀 أداء عالي • 🔒 أمان متقدم • 📱 متجاوب مع الجوال**
